package com.ylkj.system.service.impl;

import com.ylkj.common.utils.StringUtils;
import com.ylkj.system.service.IMainImageProcessingCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.ArrayList;
import java.util.List;

/**
 * 主图处理缓存服务实现类
 * 使用Redis管理主图处理状态，避免重复拉取
 *
 * <AUTHOR>
 * @date 2025-08-08
 */
@Slf4j
@Service
public class MainImageProcessingCacheService implements IMainImageProcessingCacheService {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    // 缓存key前缀
    private static final String CACHE_KEY_PREFIX = "main_image:processing:";
    private static final String FAILED_KEY_PREFIX = "main_image:failed:";
    private static final String QUEUED_KEY_PREFIX = "main_image:queued:";

    // 处理中状态过期时间（分钟）- 可配置
    @Value("${async.main-image.cache.processing-expire-minutes:5}")
    private long processingExpireMinutes;

    // 失败状态过期时间（分钟）- 可配置
    @Value("${async.main-image.cache.failed-expire-minutes:30}")
    private long failedExpireMinutes;

    // 队列状态过期时间（分钟）- 可配置（默认更长，避免未消费就过期）
    @Value("${async.main-image.cache.queued-expire-minutes:60}")
    private long queuedExpireMinutes;

    // ============ 查询状态 ============
    @Override
    public boolean isProcessing(String sku) {
        if (sku == null || sku.trim().isEmpty()) {
            return false;
        }
        String key = CACHE_KEY_PREFIX + sku;
        Boolean exists = stringRedisTemplate.hasKey(key);
        return Boolean.TRUE.equals(exists);
    }

    @Override
    public boolean isRecentlyFailed(String sku) {
        if (sku == null || sku.trim().isEmpty()) {
            return false;
        }
        String key = FAILED_KEY_PREFIX + sku;
        Boolean exists = stringRedisTemplate.hasKey(key);
        return Boolean.TRUE.equals(exists);
    }

    @Override
    public boolean isQueued(String sku) {
        if (sku == null || sku.trim().isEmpty()) {
            return false;
        }
        String key = QUEUED_KEY_PREFIX + sku;
        Boolean exists = stringRedisTemplate.hasKey(key);
        return Boolean.TRUE.equals(exists);
    }

    // ============ 标记状态 ============
    @Override
    public boolean markAsProcessing(String sku) {
        if (sku == null || StringUtils.isEmpty(sku)) {
            return false;
        }
        String key = CACHE_KEY_PREFIX + sku;
        Boolean success = stringRedisTemplate.opsForValue()
                .setIfAbsent(key, String.valueOf(System.currentTimeMillis()),
                        processingExpireMinutes, TimeUnit.MINUTES);
        if (Boolean.TRUE.equals(success)) {
            log.debug("标记SKU {} 开始处理，{}分钟后过期", sku, processingExpireMinutes);
            return true;
        } else {
            log.debug("SKU {} 已在处理中，跳过", sku);
            return false;
        }
    }

    @Override
    public Set<String> batchMarkAsProcessing(Set<String> skus) {
        Set<String> markedSkus = new HashSet<>();
        if (skus == null || skus.isEmpty()) {
            return markedSkus;
        }
        for (String sku : skus) {
            if (markAsProcessing(sku)) {
                markedSkus.add(sku);
            }
        }
        log.debug("批量标记处理中，请求数量:{}, 成功数量:{}", skus.size(), markedSkus.size());
        return markedSkus;
    }

    @Override
    public boolean markAsQueued(String sku) {
        if (sku == null || StringUtils.isEmpty(sku)) {
            return false;
        }
        String key = QUEUED_KEY_PREFIX + sku;
        Boolean success = stringRedisTemplate.opsForValue()
                .setIfAbsent(key, String.valueOf(System.currentTimeMillis()),
                        queuedExpireMinutes, TimeUnit.MINUTES);
        if (Boolean.TRUE.equals(success)) {
            log.debug("标记SKU {} 进入队列，{}分钟后过期", sku, queuedExpireMinutes);
            return true;
        }
        return false;
    }

    @Override
    public Set<String> batchMarkAsQueued(Set<String> skus) {
        Set<String> marked = new HashSet<>();
        if (skus == null || skus.isEmpty()) {
            return marked;
        }
        for (String sku : skus) {
            if (markAsQueued(sku)) {
                marked.add(sku);
            }
        }
        log.debug("批量标记排队，请求数量:{}, 成功数量:{}", skus.size(), marked.size());
        return marked;
    }

    @Override
    public void removeQueued(String sku) {
        if (sku == null || StringUtils.isEmpty(sku)) {
            return;
        }
        stringRedisTemplate.delete(QUEUED_KEY_PREFIX + sku);
    }

    @Override
    public boolean promoteQueuedToProcessing(String sku) {
        if (sku == null || StringUtils.isEmpty(sku)) {
            return false;
        }
        // 先删除队列标记，再尝试标记processing（尽力而为，可能并发下失败）
        removeQueued(sku);
        return markAsProcessing(sku);
    }

    // ============ 处理完成/失败 ============
    @Override
    public void markAsCompleted(String sku) {
        if (sku == null || StringUtils.isEmpty(sku)) {
            return;
        }
        stringRedisTemplate.delete(CACHE_KEY_PREFIX + sku);
        stringRedisTemplate.delete(QUEUED_KEY_PREFIX + sku);
        log.debug("SKU {} 处理完成，清除 processing 与 queued 标记", sku);
    }

    @Override
    public void batchMarkAsCompleted(Set<String> skus) {
        if (skus == null || skus.isEmpty()) {
            return;
        }
        Set<String> keys = new HashSet<>();
        for (String sku : skus) {
            if (sku != null && !sku.trim().isEmpty()) {
                keys.add(CACHE_KEY_PREFIX + sku);
                keys.add(QUEUED_KEY_PREFIX + sku);
            }
        }
        if (!keys.isEmpty()) {
            Long deletedCount = stringRedisTemplate.delete(keys);
            log.debug("批量清除 processing/queued 标记: {} 个", deletedCount);
        }
    }

    @Override
    public void markAsFailed(String sku) {
        if (sku == null || StringUtils.isEmpty(sku)) {
            return;
        }
        // 清processing / queued
        stringRedisTemplate.delete(CACHE_KEY_PREFIX + sku);
        stringRedisTemplate.delete(QUEUED_KEY_PREFIX + sku);
        // 写失败键
        stringRedisTemplate.opsForValue().set(FAILED_KEY_PREFIX + sku,
                String.valueOf(System.currentTimeMillis()),
                failedExpireMinutes, TimeUnit.MINUTES);
        log.debug("标记SKU {} 处理失败，{}分钟内不再重试", sku, failedExpireMinutes);
    }

    @Override
    public void batchMarkAsFailed(Set<String> skus) {
        if (skus == null || skus.isEmpty()) {
            return;
        }
        for (String sku : skus) {
            markAsFailed(sku);
        }
        log.debug("批量标记失败SKU数量: {}", skus.size());
    }

    @Override
    public void cleanExpiredCache() {
        log.debug("Redis缓存自动过期，无需手动清理");
    }

    @Override
    public String getProcessingStats() {
        return String.format("处理状态统计 - 正在处理: %d, 排队: %d, 失败等待重试: %d",
                getProcessingCount(), getQueuedCount(), getFailedCount());
    }

    @Override
    public void clearAll() {
        Set<String> processingKeys = stringRedisTemplate.keys(CACHE_KEY_PREFIX + "*");
        if (processingKeys != null && !processingKeys.isEmpty()) {
            Long n = stringRedisTemplate.delete(processingKeys);
            log.info("清除 {} 个processing标记", n);
        }
        Set<String> queuedKeys = stringRedisTemplate.keys(QUEUED_KEY_PREFIX + "*");
        if (queuedKeys != null && !queuedKeys.isEmpty()) {
            Long n = stringRedisTemplate.delete(queuedKeys);
            log.info("清除 {} 个queued标记", n);
        }
        Set<String> failedKeys = stringRedisTemplate.keys(FAILED_KEY_PREFIX + "*");
        if (failedKeys != null && !failedKeys.isEmpty()) {
            Long n = stringRedisTemplate.delete(failedKeys);
            log.info("清除 {} 个failed标记", n);
        }
    }

    @Override
    public int getProcessingCount() {
        Set<String> keys = stringRedisTemplate.keys(CACHE_KEY_PREFIX + "*");
        return keys != null ? keys.size() : 0;
    }

    @Override
    public int getFailedCount() {
        Set<String> keys = stringRedisTemplate.keys(FAILED_KEY_PREFIX + "*");
        return keys != null ? keys.size() : 0;
    }

    @Override
    public int getQueuedCount() {
        Set<String> keys = stringRedisTemplate.keys(QUEUED_KEY_PREFIX + "*");
        return keys != null ? keys.size() : 0;
    }

    @Override
    public List<String> listProcessingSkus(int limit) {
        if (limit <= 0) {
            limit = 10;
        }
        Set<String> keys = stringRedisTemplate.keys(CACHE_KEY_PREFIX + "*");
        if (keys == null || keys.isEmpty()) {
            return new ArrayList<>();
        }
        return keys.stream().limit(limit)
                .map(k -> k.substring(CACHE_KEY_PREFIX.length()))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> listFailedSkus(int limit) {
        if (limit <= 0) {
            limit = 10;
        }
        Set<String> keys = stringRedisTemplate.keys(FAILED_KEY_PREFIX + "*");
        if (keys == null || keys.isEmpty()) {
            return new ArrayList<>();
        }
        return keys.stream().limit(limit)
                .map(k -> k.substring(FAILED_KEY_PREFIX.length()))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> listQueuedSkus(int limit) {
        if (limit <= 0) {
            limit = 10;
        }
        Set<String> keys = stringRedisTemplate.keys(QUEUED_KEY_PREFIX + "*");
        if (keys == null || keys.isEmpty()) {
            return new ArrayList<>();
        }
        return keys.stream().limit(limit)
                .map(k -> k.substring(QUEUED_KEY_PREFIX.length()))
                .collect(Collectors.toList());
    }
} 