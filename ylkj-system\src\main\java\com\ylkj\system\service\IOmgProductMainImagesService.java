package com.ylkj.system.service;

import java.util.List;
import com.ylkj.system.model.domain.OmgProductMainImages;
import java.util.Map;

/**
 * 商品主图Service接口
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface IOmgProductMainImagesService 
{
    /**
     * 查询商品主图
     * 
     * @param id 商品主图主键
     * @return 商品主图
     */
    public OmgProductMainImages selectOmgProductMainImagesById(Long id);

    /**
     * 查询商品主图列表
     * 
     * @param omgProductMainImages 商品主图
     * @return 商品主图集合
     */
    public List<OmgProductMainImages> selectOmgProductMainImagesList(OmgProductMainImages omgProductMainImages);

    /**
     * 根据SKU查询商品主图列表
     * 
     * @param sku 商品SKU
     * @return 商品主图集合
     */
    public List<OmgProductMainImages> selectImagesBySku(String sku);

    /**
     * 根据SKU查询主图
     * 
     * @param sku 商品SKU
     * @return 主图信息
     */
    public OmgProductMainImages selectMainImageBySku(String sku);

    /**
     * 根据SKU和来源查询图片列表
     * 
     * @param sku 商品SKU
     * @param source 图片来源
     * @return 图片列表
     */
    public List<OmgProductMainImages> selectImagesBySkuAndSource(String sku, String source);

    /**
     * 新增商品主图
     * 
     * @param omgProductMainImages 商品主图
     * @return 结果
     */
    public int insertOmgProductMainImages(OmgProductMainImages omgProductMainImages);

    /**
     * 批量新增商品主图
     * 
     * @param imagesList 商品主图列表
     * @return 结果
     */
    public int batchInsertOmgProductMainImages(List<OmgProductMainImages> imagesList);

    /**
     * 修改商品主图
     * 
     * @param omgProductMainImages 商品主图
     * @return 结果
     */
    public int updateOmgProductMainImages(OmgProductMainImages omgProductMainImages);

    /**
     * 批量删除商品主图
     * 
     * @param ids 需要删除的商品主图主键集合
     * @return 结果
     */
    public int deleteOmgProductMainImagesByIds(Long[] ids);

    /**
     * 删除商品主图信息
     * 
     * @param id 商品主图主键
     * @return 结果
     */
    public int deleteOmgProductMainImagesById(Long id);

    /**
     * 根据SKU删除图片
     * 
     * @param sku 商品SKU
     * @return 结果
     */
    public int deleteImagesBySku(String sku);

    /**
     * 根据SKU和来源删除图片
     * 
     * @param sku 商品SKU
     * @param source 图片来源
     * @return 结果
     */
    public int deleteImagesBySkuAndSource(String sku, String source);

    /**
     * 设置主图
     * 
     * @param sku 商品SKU
     * @param imageId 图片ID
     * @return 结果
     */
    public int setMainImage(String sku, Long imageId);

    /**
     * 清除SKU的所有主图标记
     * 
     * @param sku 商品SKU
     * @return 结果
     */
    public int clearMainImageBySku(String sku);

    /**
     * 统计SKU的图片数量
     * 
     * @param sku 商品SKU
     * @return 图片数量
     */
    public int countImagesBySku(String sku);

    /**
     * 检查图片是否已存在
     * 
     * @param sku 商品SKU
     * @param ossImageUrl OSS图片URL
     * @return 是否存在
     */
    public boolean checkImageExists(String sku, String ossImageUrl);

    /**
     * 保存FindQC图片到主图表
     *
     * @param sku 商品SKU
     * @param itemId 商品ID
     * @param mallType 商城类型
     * @param originalUrls 原始图片URL列表
     * @param ossUrls OSS图片URL列表
     * @param source 图片来源
     * @return 保存成功的数量
     */
    public int saveFindQcImages(String sku, String itemId, String mallType, 
                               List<String> originalUrls, List<String> ossUrls, String source);

    /**
     * 批量根据SKU列表查询主图
     * 用于优化商品列表查询性能
     *
     * @param skuList SKU列表
     * @return SKU到主图的映射
     */
    public Map<String, OmgProductMainImages> batchSelectMainImagesBySkus(List<String> skuList);

    /**
     * 保存单张图片到主图表
     *
     * @param sku 商品SKU
     * @param itemId 商品ID
     * @param mallType 商城类型
     * @param originalUrl 原始图片URL
     * @param ossUrl OSS图片URL
     * @param source 图片来源
     * @param displayOrder 显示顺序
     * @param isMain 是否为主图
     * @return 保存结果
     */
    public int saveSingleImage(String sku, String itemId, String mallType,
                              String originalUrl, String ossUrl, String source,
                              Integer displayOrder, Integer isMain);
}
