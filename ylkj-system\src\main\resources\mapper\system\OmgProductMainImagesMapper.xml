<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.system.mapper.OmgProductMainImagesMapper">
    
    <resultMap type="OmgProductMainImages" id="OmgProductMainImagesResult">
        <result property="id"    column="id"    />
        <result property="sku"    column="sku"    />
        <result property="itemId"    column="item_id"    />
        <result property="mallType"    column="mall_type"    />
        <result property="originalImageUrl"    column="original_image_url"    />
        <result property="ossImageUrl"    column="oss_image_url"    />
        <result property="imageName"    column="image_name"    />
        <result property="imageSize"    column="image_size"    />
        <result property="imageType"    column="image_type"    />
        <result property="displayOrder"    column="display_order"    />
        <result property="isMain"    column="is_main"    />
        <result property="status"    column="status"    />
        <result property="source"    column="source"    />
        <result property="remark"    column="remark"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateTime"    column="update_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
    </resultMap>

    <sql id="selectOmgProductMainImagesVo">
        select id, sku, item_id, mall_type, original_image_url, oss_image_url, image_name, 
               image_size, image_type, display_order, is_main, status, source, remark, 
               create_time, update_time, create_by, update_by 
        from omg_product_main_images
    </sql>

    <select id="selectOmgProductMainImagesList" parameterType="OmgProductMainImages" resultMap="OmgProductMainImagesResult">
        <include refid="selectOmgProductMainImagesVo"/>
        <where>  
            <if test="sku != null  and sku != ''"> and sku = #{sku}</if>
            <if test="itemId != null  and itemId != ''"> and item_id = #{itemId}</if>
            <if test="mallType != null  and mallType != ''"> and mall_type = #{mallType}</if>
            <if test="originalImageUrl != null  and originalImageUrl != ''"> and original_image_url = #{originalImageUrl}</if>
            <if test="ossImageUrl != null  and ossImageUrl != ''"> and oss_image_url = #{ossImageUrl}</if>
            <if test="imageName != null  and imageName != ''"> and image_name like concat('%', #{imageName}, '%')</if>
            <if test="imageType != null  and imageType != ''"> and image_type = #{imageType}</if>
            <if test="displayOrder != null "> and display_order = #{displayOrder}</if>
            <if test="isMain != null "> and is_main = #{isMain}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="source != null  and source != ''"> and source = #{source}</if>
        </where>
        order by sku, display_order, create_time desc
    </select>
    
    <select id="selectOmgProductMainImagesById" parameterType="Long" resultMap="OmgProductMainImagesResult">
        <include refid="selectOmgProductMainImagesVo"/>
        where id = #{id}
    </select>

    <select id="selectImagesBySku" parameterType="String" resultMap="OmgProductMainImagesResult">
        <include refid="selectOmgProductMainImagesVo"/>
        where sku = #{sku} and status = 'active'
        order by is_main desc, display_order, create_time desc
    </select>

    <select id="selectMainImageBySku" parameterType="String" resultMap="OmgProductMainImagesResult">
        <include refid="selectOmgProductMainImagesVo"/>
        where sku = #{sku} and is_main = 1 and status = 'active'
        limit 1
    </select>

    <select id="selectImagesBySkuAndSource" resultMap="OmgProductMainImagesResult">
        <include refid="selectOmgProductMainImagesVo"/>
        where sku = #{sku} and source = #{source} and status = 'active'
        order by is_main desc, display_order, create_time desc
    </select>

    <select id="countImagesBySku" parameterType="String" resultType="int">
        select count(*) from omg_product_main_images 
        where sku = #{sku} and status = 'active'
    </select>

    <select id="checkImageExists" resultType="int">
        select count(*) from omg_product_main_images 
        where sku = #{sku} and oss_image_url = #{ossImageUrl} and status = 'active'
    </select>

    <!-- 批量根据SKU列表查询主图 -->
    <select id="batchSelectMainImagesBySkus" resultMap="OmgProductMainImagesResult">
        <include refid="selectOmgProductMainImagesVo"/>
        where sku in
        <foreach collection="skuList" item="sku" open="(" separator="," close=")">
            #{sku}
        </foreach>
        and is_main = 1 and status = 'active'
        order by sku, display_order, create_time desc
    </select>
        
    <insert id="insertOmgProductMainImages" parameterType="OmgProductMainImages" useGeneratedKeys="true" keyProperty="id">
        insert into omg_product_main_images
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="sku != null and sku != ''">sku,</if>
            <if test="itemId != null and itemId != ''">item_id,</if>
            <if test="mallType != null and mallType != ''">mall_type,</if>
            <if test="originalImageUrl != null">original_image_url,</if>
            <if test="ossImageUrl != null and ossImageUrl != ''">oss_image_url,</if>
            <if test="imageName != null">image_name,</if>
            <if test="imageSize != null">image_size,</if>
            <if test="imageType != null">image_type,</if>
            <if test="displayOrder != null">display_order,</if>
            <if test="isMain != null">is_main,</if>
            <if test="status != null">status,</if>
            <if test="source != null">source,</if>
            <if test="remark != null">remark,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateBy != null">update_by,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="sku != null and sku != ''">#{sku},</if>
            <if test="itemId != null and itemId != ''">#{itemId},</if>
            <if test="mallType != null and mallType != ''">#{mallType},</if>
            <if test="originalImageUrl != null">#{originalImageUrl},</if>
            <if test="ossImageUrl != null and ossImageUrl != ''">#{ossImageUrl},</if>
            <if test="imageName != null">#{imageName},</if>
            <if test="imageSize != null">#{imageSize},</if>
            <if test="imageType != null">#{imageType},</if>
            <if test="displayOrder != null">#{displayOrder},</if>
            <if test="isMain != null">#{isMain},</if>
            <if test="status != null">#{status},</if>
            <if test="source != null">#{source},</if>
            <if test="remark != null">#{remark},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="updateBy != null">#{updateBy},</if>
         </trim>
    </insert>

    <insert id="batchInsertOmgProductMainImages" parameterType="java.util.List">
        insert into omg_product_main_images (sku, item_id, mall_type, original_image_url, oss_image_url, 
                                           image_name, image_size, image_type, display_order, is_main, 
                                           status, source, remark, create_time, create_by)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.sku}, #{item.itemId}, #{item.mallType}, #{item.originalImageUrl}, #{item.ossImageUrl},
             #{item.imageName}, #{item.imageSize}, #{item.imageType}, #{item.displayOrder}, #{item.isMain},
             #{item.status}, #{item.source}, #{item.remark}, #{item.createTime}, #{item.createBy})
        </foreach>
    </insert>

    <update id="updateOmgProductMainImages" parameterType="OmgProductMainImages">
        update omg_product_main_images
        <trim prefix="SET" suffixOverrides=",">
            <if test="sku != null and sku != ''">sku = #{sku},</if>
            <if test="itemId != null and itemId != ''">item_id = #{itemId},</if>
            <if test="mallType != null and mallType != ''">mall_type = #{mallType},</if>
            <if test="originalImageUrl != null">original_image_url = #{originalImageUrl},</if>
            <if test="ossImageUrl != null and ossImageUrl != ''">oss_image_url = #{ossImageUrl},</if>
            <if test="imageName != null">image_name = #{imageName},</if>
            <if test="imageSize != null">image_size = #{imageSize},</if>
            <if test="imageType != null">image_type = #{imageType},</if>
            <if test="displayOrder != null">display_order = #{displayOrder},</if>
            <if test="isMain != null">is_main = #{isMain},</if>
            <if test="status != null">status = #{status},</if>
            <if test="source != null">source = #{source},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteOmgProductMainImagesById" parameterType="Long">
        delete from omg_product_main_images where id = #{id}
    </delete>

    <delete id="deleteOmgProductMainImagesByIds" parameterType="String">
        delete from omg_product_main_images where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteImagesBySku" parameterType="String">
        delete from omg_product_main_images where sku = #{sku}
    </delete>

    <delete id="deleteImagesBySkuAndSource">
        delete from omg_product_main_images where sku = #{sku} and source = #{source}
    </delete>

    <update id="setMainImage">
        update omg_product_main_images set is_main = 1, update_time = now()
        where sku = #{sku} and id = #{imageId}
    </update>

    <update id="clearMainImageBySku" parameterType="String">
        update omg_product_main_images set is_main = 0, update_time = now()
        where sku = #{sku}
    </update>

</mapper>
