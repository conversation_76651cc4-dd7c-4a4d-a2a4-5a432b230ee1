# ===========================================
# 统一异步处理配置 (针对4核8G+5M带宽海外服务器优化)
# ===========================================

# 异步任务配置
async:
  # QC更新配置
  qc-update:
    # 批处理大小 - 降低以适应带宽限制
    batch-size: 20
    
    # API请求延迟时间 - 增加以避免带宽拥塞 (毫秒)
    request-delay: 800
    
    # 任务状态缓存过期时间 (小时)
    task-status-expire-hours: 24
    
    # 是否启用批量数据库更新
    enable-batch-update: true
    
    # 超时时间 - 增加以适应海外网络延迟 (秒)
    timeout-seconds: 7200
    
    # 重试次数
    retry-count: 5
    
    # 重试延迟时间 - 增加以处理网络抖动 (毫秒)
    retry-delay: 8000
    
    # 最大并发API调用数 - 限制带宽使用
    max-concurrent-api-calls: 2

  # 主图更新配置  
  main-image:
    # 是否启用增强版异步主图更新服务
    enhanced: true
    
    # 批处理大小 - 降低以适应带宽限制
    batch-size: 15
    
    # 线程池大小 - 降低以匹配CPU核心数
    thread-pool-size: 2
    
    # 请求延迟时间 - 增加以避免带宽拥塞 (毫秒)
    request-delay: 500
    
    # 是否启用并行处理 - 保持启用但控制并发度
    enable-parallel: true
    
    # 是否启用预过滤
    enable-pre-filter: true
    
    # 是否启用批量数据库更新
    enable-batch-update: true
    
    # 超时时间 - 增加以适应海外网络延迟 (秒)
    timeout-seconds: 1000
    
    # 重试次数
    retry-count: 2
    
    # 重试延迟时间 (毫秒)
    retry-delay: 1000

    # 缓存配置 - 避免重复拉取主图
    cache:
      # 处理中状态过期时间（分钟）
      # 默认5分钟，表示一个SKU开始处理后，5分钟内不会重复处理
      processing-expire-minutes: 5
      
      # 失败状态过期时间（分钟）
      # 默认30分钟，表示处理失败的SKU，30分钟内不会重试
      failed-expire-minutes: 30

      # 排队状态过期时间（分钟）
      # 默认120分钟，避免异步未消费完排队键过期
      queued-expire-minutes: 120

  # 商品导入配置
  product-import:
    # 批处理大小
    batch-size: 100
    
    # 线程池大小 - 适配CPU核心数
    thread-pool-size: 2
    
    # 数据库批量写入大小
    db-batch-size: 200
    
    # 超时时间 (秒)
    timeout-seconds: 3600

# Spring Task 执行器配置 (统一线程池)
spring:
  task:
    execution:
      pool:
        # 核心线程数 - 基于4核CPU优化
        core-size: 3
        
        # 最大线程数 - 避免过度上下文切换
        max-size: 6
        
        # 队列容量 - 平衡内存使用和响应性
        queue-capacity: 150
        
        # 线程名前缀
        thread-name-prefix: "async-unified-"
        
        # 线程空闲时间
        keep-alive: 300s
        
        # 是否允许核心线程超时
        allow-core-thread-timeout: true
        
      shutdown:
        # 优雅关闭
        await-termination: true
        await-termination-period: 180s

# JVM和性能优化配置
performance:
  # JVM配置建议
  jvm:
    # 堆内存建议 (8G服务器)
    heap-memory: "4G"  # -Xmx4g -Xms4g
    
    # 新生代内存
    young-generation: "1G"  # -Xmn1g
    
    # GC建议
    gc-type: "G1GC"  # -XX:+UseG1GC
    
  # 连接池配置
  database:
    # 数据库连接池大小 (基于CPU核心数)
    max-pool-size: 8
    min-pool-size: 2
    
  # 缓存配置
  cache:
    # 任务状态缓存最大大小
    max-task-status-cache-size: 1000
    
    # 商品缓存大小
    max-product-cache-size: 5000

# 监控和日志配置
monitoring:
  # 性能监控
  performance:
    # 是否启用性能监控
    enabled: true
    
    # 监控间隔 (秒)
    interval: 60
    
    # 慢任务阈值 (秒)
    slow-task-threshold: 300
    
  # 带宽监控
  bandwidth:
    # 是否启用带宽监控
    enabled: true
    
    # 带宽使用率告警阈值 (%)
    alert-threshold: 80
    
    # API调用频率限制 (次/秒)
    api-rate-limit: 3

# 容错和降级配置
resilience:
  # 熔断器配置
  circuit-breaker:
    # 失败率阈值 (%)
    failure-rate-threshold: 50
    
    # 最小请求数
    minimum-number-of-calls: 10
    
    # 熔断器打开时间 (毫秒)
    wait-duration-in-open-state: 30000
    
  # 降级策略
  fallback:
    # 是否启用自动降级
    auto-degrade-enabled: true
    
    # 降级时批处理大小
    degrade-batch-size: 10
    
    # 降级时请求延迟 (毫秒)
    degrade-request-delay: 2000 