package com.ylkj.service.impl.omg;

import com.ylkj.common.config.UnifiedAsyncConfig;
import com.ylkj.controller.GoodsController;
import com.ylkj.mapper.omg.OProductsMapper;
import com.ylkj.model.domain.omg.OProducts;
import com.ylkj.service.omg.AsyncQcUpdateService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Semaphore;

/**
 * 异步QC更新服务实现类
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Slf4j
@Service
public class AsyncQcUpdateServiceImpl implements AsyncQcUpdateService {
    
    @Autowired
    private OProductsMapper oProductsMapper;
    
    @Autowired
    private GoodsController goodsController;
    
    @Autowired
    private UnifiedAsyncConfig unifiedAsyncConfig;
    
    // 任务状态缓存
    private final Map<String, TaskStatus> taskStatusMap = new ConcurrentHashMap<>();
    
    // API调用并发控制信号量 - 延迟初始化
    private volatile Semaphore apiCallSemaphore;
    
    /**
     * 获取或创建API调用信号量
     * 根据配置动态创建信号量
     */
    private Semaphore getApiCallSemaphore() {
        if (apiCallSemaphore == null) {
            synchronized (this) {
                if (apiCallSemaphore == null) {
                    int maxConcurrent = unifiedAsyncConfig.getQcUpdate().getMaxConcurrentApiCalls();
                    apiCallSemaphore = new Semaphore(maxConcurrent);
                    log.info("初始化API调用信号量，最大并发数: {}", maxConcurrent);
                }
            }
        }
        return apiCallSemaphore;
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Integer> asyncBatchUpdateProductsQc(List<OProducts> productList) {
        String taskId = "batch_qc_update_" + System.currentTimeMillis();
        TaskStatus taskStatus = new TaskStatus(taskId);
        taskStatusMap.put(taskId, taskStatus);
        
        try {
            log.info("开始异步批量更新{}个商品的QC图片数量，任务ID: {}", productList.size(), taskId);
            taskStatus.setMessage("正在批量更新QC图片数量...");
            
            // 使用批量更新优化
            int successCount = batchUpdateWithOptimization(productList, taskStatus);
            
            taskStatus.setStatus("COMPLETED");
            taskStatus.setProgress(100);
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setMessage("批量更新完成");
            
            log.info("异步批量更新QC图片数量完成，任务ID: {}, 成功: {}, 失败: {}", 
                    taskId, taskStatus.getSuccessCount(), taskStatus.getErrorCount());
            
            return CompletableFuture.completedFuture(successCount);
            
        } catch (Exception e) {
            taskStatus.setStatus("FAILED");
            taskStatus.setLastError(e);
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setMessage("批量更新失败: " + e.getMessage());
            log.error("异步批量更新QC图片数量失败，任务ID: {}", taskId, e);
            return CompletableFuture.completedFuture(0);
        }
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Integer> asyncBatchUpdateProductsQcWithPaging(int pageSize) {
        String taskId = "paging_qc_update_" + System.currentTimeMillis();
        TaskStatus taskStatus = new TaskStatus(taskId);
        taskStatusMap.put(taskId, taskStatus);
        
        try {
            log.info("开始异步分页批量更新商品QC图片数量，每页{}条，任务ID: {}", pageSize, taskId);
            taskStatus.setMessage("正在分页批量更新QC图片数量...");
            
            // 获取商品总数
            long totalCount = oProductsMapper.selectCount(null);
            long totalPages = (totalCount + pageSize - 1) / pageSize;
            
            int successCount = 0;
            int currentPage = 1;
            
            while (true) {
                // 分页查询商品
                com.baomidou.mybatisplus.extension.plugins.pagination.Page<OProducts> page = 
                        new com.baomidou.mybatisplus.extension.plugins.pagination.Page<>(currentPage, pageSize);
                
                page = oProductsMapper.selectPageBasicInfo(page);
                List<OProducts> pageProducts = page.getRecords();
                
                if (pageProducts.isEmpty()) {
                    break;
                }
                
                log.info("正在处理第{}页，共{}页，本页{}条商品", currentPage, totalPages, pageProducts.size());
                
                // 批量处理当前页的商品
                int pageSuccessCount = batchUpdateWithOptimization(pageProducts, taskStatus);
                successCount += pageSuccessCount;
                
                // 更新进度
                int progress = (int) ((double) currentPage / totalPages * 100);
                taskStatus.setProgress(progress);
                taskStatus.setMessage(String.format("正在处理第%d页，共%d页", currentPage, totalPages));
                
                currentPage++;
            }
            
            taskStatus.setStatus("COMPLETED");
            taskStatus.setProgress(100);
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setMessage("分页批量更新完成");
            
            log.info("异步分页批量更新QC图片数量完成，任务ID: {}, 成功: {}, 失败: {}", 
                    taskId, taskStatus.getSuccessCount(), taskStatus.getErrorCount());
            
            return CompletableFuture.completedFuture(successCount);
            
        } catch (Exception e) {
            taskStatus.setStatus("FAILED");
            taskStatus.setLastError(e);
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setMessage("分页批量更新失败: " + e.getMessage());
            log.error("异步分页批量更新QC图片数量失败，任务ID: {}", taskId, e);
            return CompletableFuture.completedFuture(0);
        }
    }

    @Override
    @Async("taskExecutor")
    public CompletableFuture<Integer> asyncBatchUpdateProductsQcBySku(List<String> skuList) {
        String taskId = "sku_qc_update_" + System.currentTimeMillis();
        TaskStatus taskStatus = new TaskStatus(taskId);
        taskStatusMap.put(taskId, taskStatus);
        
        try {
            log.info("开始异步根据SKU列表批量更新{}个商品的QC图片数量，任务ID: {}", skuList.size(), taskId);
            taskStatus.setMessage("正在根据SKU批量更新QC图片数量...");
            
            // 使用批量更新优化
            int successCount = batchUpdateBySkuWithOptimization(skuList, taskStatus);
            
            taskStatus.setStatus("COMPLETED");
            taskStatus.setProgress(100);
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setMessage("SKU批量更新完成");
            
            log.info("异步根据SKU批量更新QC图片数量完成，任务ID: {}, 成功: {}, 失败: {}", 
                    taskId, taskStatus.getSuccessCount(), taskStatus.getErrorCount());
            
            return CompletableFuture.completedFuture(successCount);
            
        } catch (Exception e) {
            taskStatus.setStatus("FAILED");
            taskStatus.setLastError(e);
            taskStatus.setEndTime(System.currentTimeMillis());
            taskStatus.setMessage("SKU批量更新失败: " + e.getMessage());
            log.error("异步根据SKU批量更新QC图片数量失败，任务ID: {}", taskId, e);
            return CompletableFuture.completedFuture(0);
        }
    }

    @Override
    public TaskStatus getTaskStatus(String taskId) {
        return taskStatusMap.get(taskId);
    }
    
    /**
     * 批量更新优化实现
     */
    private int batchUpdateWithOptimization(List<OProducts> productList, TaskStatus taskStatus) {
        int successCount = 0;
        int errorCount = 0;

        // 获取配置参数
        int batchSize = unifiedAsyncConfig.getQcUpdate().getBatchSize();
        long requestDelay = unifiedAsyncConfig.getQcUpdate().getRequestDelay();
        
        // 更新信号量许可数（动态调整并发度）
        int maxConcurrentCalls = unifiedAsyncConfig.getQcUpdate().getMaxConcurrentApiCalls();
        
        log.info("使用配置参数 - 批处理大小: {}, 请求延迟: {}ms, 最大并发: {}", 
                batchSize, requestDelay, maxConcurrentCalls);
        
        // 按批次处理
        for (int i = 0; i < productList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, productList.size());
            List<OProducts> batch = productList.subList(i, endIndex);

            try {
                // 批量获取QC图片数量
                Map<String, String> skuQcMap = new HashMap<>();
                List<OProducts> validProducts = new ArrayList<>();

                for (OProducts product : batch) {
                    if (product.getSku() == null || product.getSku().isEmpty()) {
                        log.warn("商品ID: {} 没有SKU，跳过更新", product.getProductId());
                        continue;
                    }

                    try {
                        // 使用信号量控制API并发调用
                        getApiCallSemaphore().acquire();
                        
                        try {
                            Object qcImagesResult = goodsController.getQcImages(product.getSku());
                            int qcImageCount = extractQcImageCount(qcImagesResult);

                            // 更新商品对象的QC字段
                            product.setQc(String.valueOf(qcImageCount));
                            validProducts.add(product);
                            skuQcMap.put(product.getSku(), String.valueOf(qcImageCount));

                            log.debug("获取商品SKU: {} 的QC图片数量: {}", product.getSku(), qcImageCount);

                            // 使用配置的延迟时间
                            Thread.sleep(requestDelay);
                            
                        } finally {
                            // 释放信号量
                            getApiCallSemaphore().release();
                        }

                    } catch (Exception e) {
                        log.error("获取商品SKU: {} 的QC图片数量失败", product.getSku(), e);
                        errorCount++;
                        // 确保异常情况下也释放信号量
                        Semaphore semaphore = getApiCallSemaphore();
                        if (semaphore.availablePermits() < maxConcurrentCalls) {
                            semaphore.release();
                        }
                    }
                }

                // 批量更新数据库
                if (!skuQcMap.isEmpty()) {
                    try {
                        // 尝试批量更新
                        int updateCount = safeBatchUpdateBySku(skuQcMap);
                        successCount += updateCount;
                        log.info("批量更新{}个商品的QC数量成功，SKU列表: {}", updateCount, skuQcMap.keySet());
                    } catch (Exception e) {
                        log.error("批量更新数据库失败，SKU数量: {}，尝试逐个更新", skuQcMap.size(), e);
                        // 如果批量更新失败，尝试逐个更新
                        for (Map.Entry<String, String> entry : skuQcMap.entrySet()) {
                            try {
                                int result = oProductsMapper.updateProductQcBySku(entry.getKey(), entry.getValue());
                                if (result > 0) {
                                    successCount += result;
                                } else {
                                    errorCount++;
                                }
                            } catch (Exception ex) {
                                log.error("逐个更新SKU: {} 失败", entry.getKey(), ex);
                                errorCount++;
                            }
                        }
                    }
                } else {
                    log.warn("第{}批没有有效的商品需要更新", (i / batchSize) + 1);
                }

            } catch (Exception e) {
                log.error("批量处理第{}批商品时发生错误", (i / batchSize) + 1, e);
                errorCount += batch.size();
            }
        }

        taskStatus.setSuccessCount(successCount);
        taskStatus.setErrorCount(errorCount);

        return successCount;
    }
    
    /**
     * 根据SKU批量更新优化实现
     */
    private int batchUpdateBySkuWithOptimization(List<String> skuList, TaskStatus taskStatus) {
        int successCount = 0;
        int errorCount = 0;

        // 获取配置参数
        int batchSize = unifiedAsyncConfig.getQcUpdate().getBatchSize();
        long requestDelay = unifiedAsyncConfig.getQcUpdate().getRequestDelay();

        // 按批次处理
        for (int i = 0; i < skuList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, skuList.size());
            List<String> batch = skuList.subList(i, endIndex);

            try {
                // 批量获取QC图片数量
                Map<String, String> skuQcMap = new HashMap<>();

                for (String sku : batch) {
                    if (sku == null || sku.isEmpty()) {
                        log.warn("SKU为空，跳过更新");
                        continue;
                    }

                    try {
                        Object qcImagesResult = goodsController.getQcImages(sku);
                        int qcImageCount = extractQcImageCount(qcImagesResult);
                        skuQcMap.put(sku, String.valueOf(qcImageCount));

                        log.debug("获取SKU: {} 的QC图片数量: {}", sku, qcImageCount);

                        // 添加延迟避免API压力
                        Thread.sleep(requestDelay);

                    } catch (Exception e) {
                        log.error("获取SKU: {} 的QC图片数量失败", sku, e);
                        errorCount++;
                    }
                }

                // 批量更新数据库
                if (!skuQcMap.isEmpty()) {
                    try {
                        // 尝试批量更新
                        int updateCount = safeBatchUpdateBySku(skuQcMap);
                        successCount += updateCount;
                        log.info("批量更新{}个商品的QC数量成功，SKU列表: {}", updateCount, skuQcMap.keySet());
                    } catch (Exception e) {
                        log.error("批量更新数据库失败，SKU数量: {}，尝试逐个更新", skuQcMap.size(), e);
                        // 如果批量更新失败，尝试逐个更新
                        for (Map.Entry<String, String> entry : skuQcMap.entrySet()) {
                            try {
                                int result = oProductsMapper.updateProductQcBySku(entry.getKey(), entry.getValue());
                                if (result > 0) {
                                    successCount += result;
                                } else {
                                    errorCount++;
                                }
                            } catch (Exception ex) {
                                log.error("逐个更新SKU: {} 失败", entry.getKey(), ex);
                                errorCount++;
                            }
                        }
                    }
                } else {
                    log.warn("第{}批没有有效的SKU需要更新", (i / batchSize) + 1);
                }

                // 更新进度
                int progress = (int) ((double) (i + batch.size()) / skuList.size() * 100);
                taskStatus.setProgress(progress);

            } catch (Exception e) {
                log.error("批量处理第{}批SKU时发生错误", (i / batchSize) + 1, e);
                errorCount += batch.size();
            }
        }

        taskStatus.setSuccessCount(successCount);
        taskStatus.setErrorCount(errorCount);

        return successCount;
    }
    
    /**
     * 安全的批量更新方法
     *
     * @param skuQcMap SKU和QC数量的映射
     * @return 更新成功的记录数
     */
    private int safeBatchUpdateBySku(Map<String, String> skuQcMap) {
        if (skuQcMap == null || skuQcMap.isEmpty()) {
            return 0;
        }

        try {
            // 尝试使用批量更新
            return oProductsMapper.batchUpdateProductsQcBySku(skuQcMap);
        } catch (Exception e) {
            log.warn("批量更新失败，尝试逐个更新，错误: {}", e.getMessage());
            // 如果批量更新失败，逐个更新
            int successCount = 0;
            for (Map.Entry<String, String> entry : skuQcMap.entrySet()) {
                try {
                    int result = oProductsMapper.updateProductQcBySku(entry.getKey(), entry.getValue());
                    successCount += result;
                } catch (Exception ex) {
                    log.error("逐个更新SKU: {} 失败", entry.getKey(), ex);
                }
            }
            return successCount;
        }
    }

    /**
     * 从QC API返回结果中提取QC图片数量
     */
    @SuppressWarnings("unchecked")
    private int extractQcImageCount(Object qcImagesResult) {
        int qcImageCount = 0;

        if (qcImagesResult instanceof Map) {
            Map<String, Object> resultMap = (Map<String, Object>) qcImagesResult;

            if (resultMap.containsKey("total")) {
                Object totalObj = resultMap.get("total");
                if (totalObj instanceof Integer) {
                    qcImageCount = (Integer) totalObj;
                } else if (totalObj instanceof Number) {
                    qcImageCount = ((Number) totalObj).intValue();
                }
            } else if (resultMap.containsKey("images") && resultMap.get("images") instanceof List) {
                qcImageCount = ((List<?>) resultMap.get("images")).size();
            } else if (resultMap.containsKey("productImages") && resultMap.get("productImages") instanceof List) {
                qcImageCount = ((List<?>) resultMap.get("productImages")).size();
            }
        }

        return qcImageCount;
    }
}
