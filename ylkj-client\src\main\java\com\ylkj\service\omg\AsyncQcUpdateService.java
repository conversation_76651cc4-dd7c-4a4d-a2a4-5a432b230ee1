package com.ylkj.service.omg;

import com.ylkj.model.domain.omg.OProducts;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 异步QC更新服务接口
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface AsyncQcUpdateService {
    
    /**
     * 异步批量更新商品QC图片数量
     * 
     * @param productList 商品列表
     * @return 异步任务结果
     */
    CompletableFuture<Integer> asyncBatchUpdateProductsQc(List<OProducts> productList);
    
    /**
     * 异步分页批量更新商品QC图片数量
     * 
     * @param pageSize 每页处理的商品数量
     * @return 异步任务结果
     */
    CompletableFuture<Integer> asyncBatchUpdateProductsQcWithPaging(int pageSize);
    
    /**
     * 异步根据SKU列表批量更新商品QC图片数量
     * 
     * @param skuList SKU列表
     * @return 异步任务结果
     */
    CompletableFuture<Integer> asyncBatchUpdateProductsQcBySku(List<String> skuList);
    
    /**
     * 获取任务执行状态
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    TaskStatus getTaskStatus(String taskId);
    
    /**
     * 任务状态信息
     */
    class TaskStatus {
        private String taskId;
        private String status; // RUNNING, COMPLETED, FAILED
        private int progress; // 0-100
        private String message;
        private long startTime;
        private long endTime;
        private int successCount;
        private int errorCount;
        private Exception lastError;
        
        // 构造函数
        public TaskStatus(String taskId) {
            this.taskId = taskId;
            this.status = "RUNNING";
            this.progress = 0;
            this.startTime = System.currentTimeMillis();
        }
        
        // Getters and Setters
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        
        public String getStatus() { return status; }
        public void setStatus(String status) { this.status = status; }
        
        public int getProgress() { return progress; }
        public void setProgress(int progress) { this.progress = progress; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public long getStartTime() { return startTime; }
        public void setStartTime(long startTime) { this.startTime = startTime; }
        
        public long getEndTime() { return endTime; }
        public void setEndTime(long endTime) { this.endTime = endTime; }
        
        public int getSuccessCount() { return successCount; }
        public void setSuccessCount(int successCount) { this.successCount = successCount; }
        
        public int getErrorCount() { return errorCount; }
        public void setErrorCount(int errorCount) { this.errorCount = errorCount; }
        
        public Exception getLastError() { return lastError; }
        public void setLastError(Exception lastError) { this.lastError = lastError; }
        
        public long getDuration() {
            return endTime > 0 ? endTime - startTime : System.currentTimeMillis() - startTime;
        }
    }
}
