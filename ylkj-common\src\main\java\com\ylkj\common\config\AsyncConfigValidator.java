package com.ylkj.common.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 异步配置验证器
 * 在应用启动时验证配置是否正确加载
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Slf4j
@Component
@Order(1000) // 确保在其他组件之后执行
public class AsyncConfigValidator implements CommandLineRunner {
    
    @Autowired
    private UnifiedAsyncConfig unifiedAsyncConfig;
    
    @Override
    public void run(String... args) throws Exception {
        validateAsyncConfig();
    }
    
    /**
     * 验证异步配置
     */
    private void validateAsyncConfig() {
        try {
            log.info("=== 异步配置验证开始 ===");
            
            // 验证QC更新配置
            UnifiedAsyncConfig.QcUpdateConfig qcConfig = unifiedAsyncConfig.getQcUpdate();
            log.info("QC更新配置: 批处理大小={}, 请求延迟={}ms, 最大并发={}", 
                    qcConfig.getBatchSize(), 
                    qcConfig.getRequestDelay(), 
                    qcConfig.getMaxConcurrentApiCalls());
            
            // 验证主图更新配置
            UnifiedAsyncConfig.MainImageConfig imageConfig = unifiedAsyncConfig.getMainImage();
            log.info("主图更新配置: 批处理大小={}, 请求延迟={}ms, 线程池大小={}", 
                    imageConfig.getBatchSize(), 
                    imageConfig.getRequestDelay(), 
                    imageConfig.getThreadPoolSize());
            
            // 验证商品导入配置
            UnifiedAsyncConfig.ProductImportConfig importConfig = unifiedAsyncConfig.getProductImport();
            log.info("商品导入配置: 批处理大小={}, 线程池大小={}, 数据库批次大小={}", 
                    importConfig.getBatchSize(), 
                    importConfig.getThreadPoolSize(), 
                    importConfig.getDbBatchSize());
            
            // 验证推荐线程池配置
            UnifiedAsyncConfig.ThreadPoolConfig threadConfig = unifiedAsyncConfig.getOptimalThreadPoolConfig();
            log.info("推荐线程池配置: 核心线程={}, 最大线程={}, 队列容量={}", 
                    threadConfig.getCorePoolSize(), 
                    threadConfig.getMaxPoolSize(), 
                    threadConfig.getQueueCapacity());
            
            // 验证配置合理性
            validateConfigReasonable(qcConfig, imageConfig, importConfig);
            
            log.info("=== 异步配置验证成功 ✅ ===");
            
        } catch (Exception e) {
            log.error("=== 异步配置验证失败 ❌ ===", e);
            throw new RuntimeException("异步配置验证失败", e);
        }
    }
    
    /**
     * 验证配置参数的合理性
     */
    private void validateConfigReasonable(
            UnifiedAsyncConfig.QcUpdateConfig qcConfig, 
            UnifiedAsyncConfig.MainImageConfig imageConfig, 
            UnifiedAsyncConfig.ProductImportConfig importConfig) {
        
        // 验证批处理大小
        if (qcConfig.getBatchSize() <= 0 || qcConfig.getBatchSize() > 100) {
            log.warn("QC批处理大小可能不合理: {}", qcConfig.getBatchSize());
        }
        
        if (imageConfig.getBatchSize() <= 0 || imageConfig.getBatchSize() > 50) {
            log.warn("主图批处理大小可能不合理: {}", imageConfig.getBatchSize());
        }
        
        // 验证延迟时间
        if (qcConfig.getRequestDelay() < 100) {
            log.warn("QC请求延迟可能过短，建议>=100ms: {}ms", qcConfig.getRequestDelay());
        }
        
        if (imageConfig.getRequestDelay() < 100) {
            log.warn("主图请求延迟可能过短，建议>=100ms: {}ms", imageConfig.getRequestDelay());
        }
        
        // 验证并发数
        if (qcConfig.getMaxConcurrentApiCalls() <= 0) {
            log.warn("QC最大并发调用数必须>0: {}", qcConfig.getMaxConcurrentApiCalls());
        }
        
        if (qcConfig.getMaxConcurrentApiCalls() > 10) {
            log.warn("QC最大并发调用数过大，可能导致带宽拥塞: {}", qcConfig.getMaxConcurrentApiCalls());
        }
        
        // 验证线程池大小
        if (imageConfig.getThreadPoolSize() > Runtime.getRuntime().availableProcessors() * 2) {
            log.warn("主图线程池大小过大，可能导致上下文切换开销: {} (CPU核心数: {})", 
                    imageConfig.getThreadPoolSize(), 
                    Runtime.getRuntime().availableProcessors());
        }
        
        log.info("配置合理性检查完成");
    }
} 