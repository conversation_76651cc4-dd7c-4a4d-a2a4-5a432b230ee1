package com.ylkj.system.service;

import java.util.Set;
import java.util.List;

/**
 * 主图处理缓存服务接口
 * 用于管理主图处理状态，避免重复拉取
 * 
 * <AUTHOR>
 * @date 2025-08-08
 */
public interface IMainImageProcessingCacheService {
    
    // ============ 查询状态 ============
    boolean isProcessing(String sku);
    boolean isRecentlyFailed(String sku);
    
    /** 是否已在队列中等待处理 */
    boolean isQueued(String sku);

    // ============ 标记状态 ============
    boolean markAsProcessing(String sku);
    Set<String> batchMarkAsProcessing(Set<String> skus);

    /** 将SKU标记为排队 */
    boolean markAsQueued(String sku);

    /** 批量标记排队 */
    Set<String> batchMarkAsQueued(Set<String> skus);

    /** 从队列中移除（不改变processing/failed状态） */
    void removeQueued(String sku);

    /** 将队列中的SKU提升为处理中（尽力而为，非强一致） */
    boolean promoteQueuedToProcessing(String sku);

    // ============ 处理完成/失败 ============
    void markAsCompleted(String sku);
    void batchMarkAsCompleted(Set<String> skus);
    void markAsFailed(String sku);
    void batchMarkAsFailed(Set<String> skus);

    // ============ 清理与统计 ============
    void cleanExpiredCache();
    String getProcessingStats();
    void clearAll();
    int getProcessingCount();
    int getFailedCount();

    /** 获取排队中的SKU数量 */
    int getQueuedCount();

    // ============ 列表可观测 ============
    List<String> listProcessingSkus(int limit);
    List<String> listFailedSkus(int limit);

    /** 返回部分排队中的SKU列表（用于观测） */
    List<String> listQueuedSkus(int limit);
} 