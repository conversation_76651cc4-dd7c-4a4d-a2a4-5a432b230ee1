package com.ylkj.system.mapper;

import java.util.List;
import com.ylkj.system.model.domain.OmgProductMainImages;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * 商品主图Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
public interface OmgProductMainImagesMapper extends BaseMapper<OmgProductMainImages>
{
    /**
     * 查询商品主图
     * 
     * @param id 商品主图主键
     * @return 商品主图
     */
    public OmgProductMainImages selectOmgProductMainImagesById(Long id);

    /**
     * 查询商品主图列表
     * 
     * @param omgProductMainImages 商品主图
     * @return 商品主图集合
     */
    public List<OmgProductMainImages> selectOmgProductMainImagesList(OmgProductMainImages omgProductMainImages);

    /**
     * 根据SKU查询商品主图列表
     * 
     * @param sku 商品SKU
     * @return 商品主图集合
     */
    public List<OmgProductMainImages> selectImagesBySku(@Param("sku") String sku);

    /**
     * 根据SKU查询主图
     * 
     * @param sku 商品SKU
     * @return 主图信息
     */
    public OmgProductMainImages selectMainImageBySku(@Param("sku") String sku);

    /**
     * 根据SKU和来源查询图片列表
     * 
     * @param sku 商品SKU
     * @param source 图片来源
     * @return 图片列表
     */
    public List<OmgProductMainImages> selectImagesBySkuAndSource(@Param("sku") String sku, @Param("source") String source);

    /**
     * 新增商品主图
     * 
     * @param omgProductMainImages 商品主图
     * @return 结果
     */
    public int insertOmgProductMainImages(OmgProductMainImages omgProductMainImages);

    /**
     * 批量新增商品主图
     * 
     * @param imagesList 商品主图列表
     * @return 结果
     */
    public int batchInsertOmgProductMainImages(@Param("list") List<OmgProductMainImages> imagesList);

    /**
     * 批量根据SKU列表查询主图
     * 用于优化商品列表查询性能
     * 
     * @param skuList SKU列表
     * @return 主图列表
     */
    public List<OmgProductMainImages> batchSelectMainImagesBySkus(@Param("skuList") List<String> skuList);

    /**
     * 修改商品主图
     * 
     * @param omgProductMainImages 商品主图
     * @return 结果
     */
    public int updateOmgProductMainImages(OmgProductMainImages omgProductMainImages);

    /**
     * 删除商品主图
     * 
     * @param id 商品主图主键
     * @return 结果
     */
    public int deleteOmgProductMainImagesById(Long id);

    /**
     * 批量删除商品主图
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOmgProductMainImagesByIds(Long[] ids);

    /**
     * 根据SKU删除图片
     * 
     * @param sku 商品SKU
     * @return 结果
     */
    public int deleteImagesBySku(@Param("sku") String sku);

    /**
     * 根据SKU和来源删除图片
     * 
     * @param sku 商品SKU
     * @param source 图片来源
     * @return 结果
     */
    public int deleteImagesBySkuAndSource(@Param("sku") String sku, @Param("source") String source);

    /**
     * 设置主图
     * 
     * @param sku 商品SKU
     * @param imageId 图片ID
     * @return 结果
     */
    public int setMainImage(@Param("sku") String sku, @Param("imageId") Long imageId);

    /**
     * 清除SKU的所有主图标记
     * 
     * @param sku 商品SKU
     * @return 结果
     */
    public int clearMainImageBySku(@Param("sku") String sku);

    /**
     * 统计SKU的图片数量
     * 
     * @param sku 商品SKU
     * @return 图片数量
     */
    public int countImagesBySku(@Param("sku") String sku);

    /**
     * 检查图片是否已存在
     * 
     * @param sku 商品SKU
     * @param ossImageUrl OSS图片URL
     * @return 存在的记录数
     */
    public int checkImageExists(@Param("sku") String sku, @Param("ossImageUrl") String ossImageUrl);
}
