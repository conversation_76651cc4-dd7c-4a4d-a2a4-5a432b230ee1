package com.ylkj.controller.omg;


import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ylkj.common.config.RuoYiConfig;
import com.ylkj.common.core.domain.AjaxResult;
import com.ylkj.common.utils.file.FileUploadUtils;
import com.ylkj.common.utils.file.FileUtils;
import com.ylkj.common.utils.file.OssUploadUtils;
import com.ylkj.framework.config.ServerConfig;
import com.ylkj.model.domain.Products;
import com.ylkj.model.domain.omg.OProducts;
import com.ylkj.model.vo.OProductsVO;
import com.ylkj.model.vo.ProductsVo;
import com.ylkj.service.omg.OIProductsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;
import java.util.ArrayList;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <p>
 * omg_商品表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
@RestController
@RequestMapping("/front/omg/products")
public class OProductsController {

    @Autowired
    private OIProductsService oProductsService;
    @Autowired
    private ServerConfig serverConfig;


    /**
     * @author: 小许
     * @date: 2025/5/15/周四 16:45
     * @description: 通过搜索商品
     * @param oProductsVO
     * @return AjaxResult
     */
    @RequestMapping("/search")
    public AjaxResult searchProducts(@RequestBody OProductsVO oProductsVO) {

        PageHelper.startPage(oProductsVO.getPage(), oProductsVO.getPageSize());
        List<OProducts> products = oProductsService.searchProducts(oProductsVO);
        // 下面这句查询会自动被分页插件拦截并加上 LIMIT 和 OFFSET
        PageInfo<OProducts> page = new PageInfo<>(products);

        if (products == null) {
            return AjaxResult.error("搜索商品失败");
        }


        return AjaxResult.success(page);
    }


    /**
     * @author: 小许
     * @date: 2025/5/15/周四 18:51
     * @description: 根据分类ID或品牌ID获取商品列表
     * @param categoryId
     * @param brandId
     * @return AjaxResult
     */
    @RequestMapping("/categories")
    public AjaxResult getProductsByCategory(Long categoryId,Long brandId,Integer page,Integer pageSize,String merchant, Boolean recommend) {
        PageHelper.startPage(page, pageSize);
        List<OProducts> productsByCategory = oProductsService.getProductsByCategory(categoryId,brandId,merchant,recommend);
        PageInfo<OProducts> oProductsPageInfo = new PageInfo<>(productsByCategory);
        if (productsByCategory == null) {
            return AjaxResult.error("获取商品分类失败");
        }

        return AjaxResult.success(oProductsPageInfo);
    }


    @GetMapping("/{id}")
    public AjaxResult getProductById(@PathVariable("id") Long id) {

        OProductsVO productsVo = oProductsService.getProductById(id);
        if (productsVo == null) {
            return AjaxResult.error("商品不存在");
        }

        return AjaxResult.success(productsVo);
    }

    /**
     * @author: 小许
     * @date: 2025/5/19/周一10:05
     * @description: 更新商品浏览量
     * @param productId
     * @return AjaxResult
     */
    @PostMapping("/{productId}/view")
    public AjaxResult updateViews(@PathVariable("productId") Long productId) {

        System.err.println(productId);
        int result = oProductsService.updateViews(productId);
        if (result > 0) {
            return AjaxResult.success("更新成功");
        } else {
            return AjaxResult.error("更新失败");
        }
    }

    /**
     * @Author: 吴居乐
     * @Description: 通过商品id获取推荐商品
     * @DateTime: 11:36 2025/4/23/周三
     * @Params: [products]
     * @Return java.lang.Object
     */
    @RequestMapping("/recommend")
    public AjaxResult getRecommendProducts(Products products) {

        List<OProducts> recommendProducts = oProductsService.getRecommendProducts(products);

        if (recommendProducts == null) {
            return AjaxResult.error("获取推荐商品失败");
        }

        return AjaxResult.success(recommendProducts);
    }


    /**
     * 通用上传请求（单个）- 使用阿里云OSS
     */
    @PostMapping("/upload")
    public AjaxResult uploadFile(MultipartFile file) throws Exception
    {
        try
        {
            // 使用阿里云OSS上传
            String url = OssUploadUtils.upload(file);
            
            AjaxResult ajax = AjaxResult.success();
            ajax.put("url", url);
            ajax.put("fileName", url);
            ajax.put("newFileName", FileUtils.getName(url));
            ajax.put("originalFilename", file.getOriginalFilename());
            return ajax;
        }
        catch (Exception e)
        {
            return AjaxResult.error(e.getMessage());
        }
    }


    /**
     * @author: 小许
     * @date: 2025/5/19/周一10:05
     * @description: 获取所有商户
     * @return AjaxResult
     */
    @RequestMapping("/getAllMerchant")
    public AjaxResult getAllMerchant() {

        List<String> allMerchant = oProductsService.getAllMerchant();
        if (allMerchant == null) {
            return AjaxResult.error("获取所有商户失败");
        }

        System.err.println(allMerchant);
        return AjaxResult.success(allMerchant);
    }

    /**
     * 批量更新所有商品的QC图片数量
     * 
     * @return 更新结果
     */
    @PostMapping("/qc-update/batch-all")
    public AjaxResult batchUpdateQcCount() {
        try {
            int successCount = oProductsService.batchUpdateProductsQcCount();
            return AjaxResult.success("批量更新QC图片数量成功", successCount);
        } catch (Exception e) {
            return AjaxResult.error("批量更新QC图片数量失败：" + e.getMessage());
        }
    }
    
    /**
     * 分页批量更新商品的QC图片数量
     * 
     * @param pageSize 每页处理的商品数量，默认为20
     * @return 更新结果
     */
    @PostMapping("/qc-update/batch-paging")
    public AjaxResult batchUpdateQcCountWithPaging(@RequestParam(defaultValue = "20") int pageSize) {
        try {
            int successCount = oProductsService.batchUpdateProductsQcCountWithPaging(pageSize);
            return AjaxResult.success("分页批量更新QC图片数量成功", successCount);
        } catch (Exception e) {
            return AjaxResult.error("分页批量更新QC图片数量失败：" + e.getMessage());
        }
    }
    
    /**
     * 根据SKU列表批量更新商品的QC图片数量
     * 
     * @param skuList 要更新的SKU列表
     * @return 更新结果
     */
    @PostMapping("/qc-update/batch-by-sku")
    public AjaxResult batchUpdateQcCountBySku(@RequestBody List<String> skuList) {
        try {
            int successCount = oProductsService.batchUpdateProductsQcCountBySku(skuList);
            return AjaxResult.success("根据SKU列表批量更新QC图片数量成功", successCount);
        } catch (Exception e) {
            return AjaxResult.error("根据SKU列表批量更新QC图片数量失败：" + e.getMessage());
        }
    }

    /**
     * 获取推荐商品
     *
     * @param page 页码
     * @param pageSize 每页数量
     * @return 推荐商品列表
     */
    @GetMapping("/recommended")
    public AjaxResult getRecommendedProducts(
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "20") Integer pageSize) {

        try {
            // 设置分页参数
            PageHelper.startPage(page, pageSize);
            
            // 获取状态为1和2的商品
            List<Integer> statusList = new ArrayList<>();
            statusList.add(1);
            statusList.add(2);
            
            List<OProducts> products = oProductsService.getProductsByStatus(statusList);
            
            // 封装分页信息
            PageInfo<OProducts> pageInfo = new PageInfo<>(products);
            
            return AjaxResult.success(pageInfo);
        } catch (Exception e) {
            return AjaxResult.error("获取推荐商品失败: " + e.getMessage());
        }
    }

    /**
     * 清除商品详情缓存
     * 
     * @param sku 商品SKU
     * @param platform 平台名称
     * @return 清除结果
     */
    @DeleteMapping("/cache/clear")
    public AjaxResult clearProductDetailCache(@RequestParam String sku, @RequestParam String platform) {
        try {
            boolean success = oProductsService.clearProductDetailCache(sku, platform);
            if (success) {
                return AjaxResult.success("商品详情缓存清除成功");
            } else {
                return AjaxResult.error("商品详情缓存清除失败或缓存不存在");
            }
        } catch (Exception e) {
            return AjaxResult.error("清除缓存时发生错误: " + e.getMessage());
        }
    }

    /**
     * 检查商品详情缓存是否存在
     * 
     * @param sku 商品SKU
     * @param platform 平台名称
     * @return 检查结果
     */
    @GetMapping("/cache/exists")
    public AjaxResult hasProductDetailCache(@RequestParam String sku, @RequestParam String platform) {
        try {
            boolean exists = oProductsService.hasProductDetailCache(sku, platform);
            return AjaxResult.success("缓存状态查询成功", exists);
        } catch (Exception e) {
            return AjaxResult.error("检查缓存状态时发生错误: " + e.getMessage());
        }
    }
    
    /**
     * 强制刷新商品详情缓存
     * 用于当数据库数据更新后，手动触发缓存更新
     * 
     * @param id 商品ID
     * @return 刷新结果
     */
    @PostMapping("/cache/refresh/{id}")
    public AjaxResult refreshProductCache(@PathVariable("id") Long id) {
        try {
            // 先查询商品基本信息
            OProductsVO basicProductInfo = oProductsService.getProductById(id);
            if (basicProductInfo == null) {
                return AjaxResult.error("商品不存在，无法刷新缓存");
            }
            
            // 清除旧缓存
            boolean cleared = oProductsService.clearProductDetailCache(basicProductInfo.getSku(), basicProductInfo.getPlatform());
            
            // 重新查询商品详情，这会触发缓存重建
            OProductsVO refreshed = oProductsService.getProductById(id);
            
            if (refreshed != null) {
                return AjaxResult.success("商品详情缓存刷新成功");
            } else {
                return AjaxResult.error("商品详情缓存刷新失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("刷新缓存时发生错误: " + e.getMessage());
        }
    }

    /**
     * 清除所有商品详情缓存
     * 
     * @return 清除结果
     */
    @DeleteMapping("/cache/clear-all")
    public AjaxResult clearAllProductDetailCache() {
        try {
            int clearedCount = oProductsService.clearAllProductDetailCache();
            if (clearedCount >= 0) {
                return AjaxResult.success("所有商品详情缓存清除成功，共清除 " + clearedCount + " 个缓存", clearedCount);
            } else {
                return AjaxResult.error("清除所有商品详情缓存失败");
            }
        } catch (Exception e) {
            return AjaxResult.error("清除所有缓存时发生错误: " + e.getMessage());
        }
    }


    // 获取所有商品数量
    @GetMapping("/allCount")
    public AjaxResult getAllProductsCount() {
       return AjaxResult.success("获取所有商品数量成功", oProductsService.getAllProductsCount().size());
    }


}
