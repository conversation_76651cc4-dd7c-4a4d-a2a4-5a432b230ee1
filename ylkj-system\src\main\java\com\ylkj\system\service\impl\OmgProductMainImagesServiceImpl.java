package com.ylkj.system.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import com.ylkj.common.utils.SecurityUtils;
import com.ylkj.system.mapper.OmgProductMainImagesMapper;
import com.ylkj.system.model.domain.OmgProductMainImages;
import com.ylkj.system.service.IOmgProductMainImagesService;
import lombok.extern.slf4j.Slf4j;

/**
 * 商品主图Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-01-19
 */
@Slf4j
@Service
public class OmgProductMainImagesServiceImpl implements IOmgProductMainImagesService 
{
    @Autowired
    private OmgProductMainImagesMapper omgProductMainImagesMapper;

    /**
     * 查询商品主图
     * 
     * @param id 商品主图主键
     * @return 商品主图
     */
    @Override
    public OmgProductMainImages selectOmgProductMainImagesById(Long id)
    {
        return omgProductMainImagesMapper.selectOmgProductMainImagesById(id);
    }

    /**
     * 查询商品主图列表
     * 
     * @param omgProductMainImages 商品主图
     * @return 商品主图
     */
    @Override
    public List<OmgProductMainImages> selectOmgProductMainImagesList(OmgProductMainImages omgProductMainImages)
    {
        return omgProductMainImagesMapper.selectOmgProductMainImagesList(omgProductMainImages);
    }

    /**
     * 根据SKU查询商品主图列表
     * 
     * @param sku 商品SKU
     * @return 商品主图集合
     */
    @Override
    public List<OmgProductMainImages> selectImagesBySku(String sku)
    {
        return omgProductMainImagesMapper.selectImagesBySku(sku);
    }

    /**
     * 根据SKU查询主图
     * 
     * @param sku 商品SKU
     * @return 主图信息
     */
    @Override
    public OmgProductMainImages selectMainImageBySku(String sku)
    {
        return omgProductMainImagesMapper.selectMainImageBySku(sku);
    }

    /**
     * 根据SKU和来源查询图片列表
     * 
     * @param sku 商品SKU
     * @param source 图片来源
     * @return 图片列表
     */
    @Override
    public List<OmgProductMainImages> selectImagesBySkuAndSource(String sku, String source)
    {
        return omgProductMainImagesMapper.selectImagesBySkuAndSource(sku, source);
    }

    /**
     * 新增商品主图
     * 
     * @param omgProductMainImages 商品主图
     * @return 结果
     */
    @Override
    public int insertOmgProductMainImages(OmgProductMainImages omgProductMainImages)
    {
        omgProductMainImages.setCreateTime(new Date());
        try {
            omgProductMainImages.setCreateBy(SecurityUtils.getUsername());
        } catch (Exception e) {
            omgProductMainImages.setCreateBy("system");
        }
        return omgProductMainImagesMapper.insertOmgProductMainImages(omgProductMainImages);
    }

    /**
     * 批量新增商品主图
     * 
     * @param imagesList 商品主图列表
     * @return 结果
     */
    @Override
    public int batchInsertOmgProductMainImages(List<OmgProductMainImages> imagesList)
    {
        if (imagesList == null || imagesList.isEmpty()) {
            return 0;
        }

        String currentUser;
        try {
            currentUser = SecurityUtils.getUsername();
        } catch (Exception e) {
            currentUser = "system";
        }

        Date now = new Date();
        for (OmgProductMainImages image : imagesList) {
            image.setCreateTime(now);
            image.setCreateBy(currentUser);
            if (image.getStatus() == null) {
                image.setStatus("active");
            }
        }

        return omgProductMainImagesMapper.batchInsertOmgProductMainImages(imagesList);
    }

    /**
     * 修改商品主图
     * 
     * @param omgProductMainImages 商品主图
     * @return 结果
     */
    @Override
    public int updateOmgProductMainImages(OmgProductMainImages omgProductMainImages)
    {
        omgProductMainImages.setUpdateTime(new Date());
        try {
            omgProductMainImages.setUpdateBy(SecurityUtils.getUsername());
        } catch (Exception e) {
            omgProductMainImages.setUpdateBy("system");
        }
        return omgProductMainImagesMapper.updateOmgProductMainImages(omgProductMainImages);
    }

    /**
     * 批量删除商品主图
     * 
     * @param ids 需要删除的商品主图主键
     * @return 结果
     */
    @Override
    public int deleteOmgProductMainImagesByIds(Long[] ids)
    {
        return omgProductMainImagesMapper.deleteOmgProductMainImagesByIds(ids);
    }

    /**
     * 删除商品主图信息
     * 
     * @param id 商品主图主键
     * @return 结果
     */
    @Override
    public int deleteOmgProductMainImagesById(Long id)
    {
        return omgProductMainImagesMapper.deleteOmgProductMainImagesById(id);
    }

    /**
     * 根据SKU删除图片
     * 
     * @param sku 商品SKU
     * @return 结果
     */
    @Override
    public int deleteImagesBySku(String sku)
    {
        return omgProductMainImagesMapper.deleteImagesBySku(sku);
    }

    /**
     * 根据SKU和来源删除图片
     * 
     * @param sku 商品SKU
     * @param source 图片来源
     * @return 结果
     */
    @Override
    public int deleteImagesBySkuAndSource(String sku, String source)
    {
        return omgProductMainImagesMapper.deleteImagesBySkuAndSource(sku, source);
    }

    /**
     * 设置主图
     * 
     * @param sku 商品SKU
     * @param imageId 图片ID
     * @return 结果
     */
    @Override
    public int setMainImage(String sku, Long imageId)
    {
        // 先清除该SKU的所有主图标记
        omgProductMainImagesMapper.clearMainImageBySku(sku);
        // 再设置新的主图
        return omgProductMainImagesMapper.setMainImage(sku, imageId);
    }

    /**
     * 清除SKU的所有主图标记
     * 
     * @param sku 商品SKU
     * @return 结果
     */
    @Override
    public int clearMainImageBySku(String sku)
    {
        return omgProductMainImagesMapper.clearMainImageBySku(sku);
    }

    /**
     * 统计SKU的图片数量
     * 
     * @param sku 商品SKU
     * @return 图片数量
     */
    @Override
    public int countImagesBySku(String sku)
    {
        return omgProductMainImagesMapper.countImagesBySku(sku);
    }

    /**
     * 检查图片是否已存在
     *
     * @param sku 商品SKU
     * @param ossImageUrl OSS图片URL
     * @return 是否存在
     */
    @Override
    public boolean checkImageExists(String sku, String ossImageUrl)
    {
        return omgProductMainImagesMapper.checkImageExists(sku, ossImageUrl) > 0;
    }

    /**
     * 保存FindQC图片到主图表
     *
     * @param sku 商品SKU
     * @param itemId 商品ID
     * @param mallType 商城类型
     * @param originalUrls 原始图片URL列表
     * @param ossUrls OSS图片URL列表
     * @param source 图片来源
     * @return 保存成功的数量
     */
    @Override
    public int saveFindQcImages(String sku, String itemId, String mallType, 
                               List<String> originalUrls, List<String> ossUrls, String source) {
        if (originalUrls == null || ossUrls == null || originalUrls.size() != ossUrls.size()) {
            log.warn("图片URL列表不匹配，SKU: {}", sku);
            return 0;
        }

        List<OmgProductMainImages> imagesList = new ArrayList<>();
        for (int i = 0; i < originalUrls.size(); i++) {
            OmgProductMainImages image = new OmgProductMainImages();
            image.setSku(sku);
            image.setItemId(itemId);
            image.setMallType(mallType);
            image.setOriginalImageUrl(originalUrls.get(i));
            image.setOssImageUrl(ossUrls.get(i));
            image.setImageName("FindQC_" + sku + "_" + (i + 1));
            image.setImageType("jpg");
            image.setDisplayOrder(i + 1);
            image.setIsMain(i == 0 ? 1 : 0); // 第一张图片设为主图
            image.setStatus("active");
            image.setSource(source);
            image.setRemark("FindQC自动处理");
            
            imagesList.add(image);
        }

        return batchInsertOmgProductMainImages(imagesList);
    }

    /**
     * 批量根据SKU列表查询主图
     * 用于优化商品列表查询性能
     *
     * @param skuList SKU列表
     * @return SKU到主图的映射
     */
    @Override
    public Map<String, OmgProductMainImages> batchSelectMainImagesBySkus(List<String> skuList) {
        if (skuList == null || skuList.isEmpty()) {
            return new HashMap<>();
        }

        try {
            List<OmgProductMainImages> mainImages = omgProductMainImagesMapper.batchSelectMainImagesBySkus(skuList);
            
            Map<String, OmgProductMainImages> resultMap = new HashMap<>();
            for (OmgProductMainImages image : mainImages) {
                if (image.getSku() != null) {
                    resultMap.put(image.getSku(), image);
                }
            }
            
            return resultMap;
        } catch (Exception e) {
            log.error("批量查询主图失败: {}", e.getMessage());
            return new HashMap<>();
        }
    }

    /**
     * 保存单张图片到主图表
     *
     * @param sku 商品SKU
     * @param itemId 商品ID
     * @param mallType 商城类型
     * @param originalUrl 原始图片URL
     * @param ossUrl OSS图片URL
     * @param source 图片来源
     * @param displayOrder 显示顺序
     * @param isMain 是否为主图
     * @return 保存结果
     */
    @Override
    public int saveSingleImage(String sku, String itemId, String mallType,
                              String originalUrl, String ossUrl, String source,
                              Integer displayOrder, Integer isMain)
    {
        if (!StringUtils.hasText(ossUrl)) {
            log.warn("OSS图片URL为空，SKU: {}", sku);
            return 0;
        }

        // 检查图片是否已存在
        if (checkImageExists(sku, ossUrl)) {
            log.debug("图片已存在，跳过保存，SKU: {}, OSS URL: {}", sku, ossUrl);
            return 0;
        }

        OmgProductMainImages image = new OmgProductMainImages();
        image.setSku(sku);
        image.setItemId(itemId);
        image.setMallType(mallType);
        image.setOriginalImageUrl(originalUrl);
        image.setOssImageUrl(ossUrl);
        image.setDisplayOrder(displayOrder != null ? displayOrder : 1);
        image.setIsMain(isMain != null ? isMain : 0);
        image.setStatus("active");
        image.setSource(source);

        // 从OSS URL中提取文件名和类型
        extractImageInfo(image, ossUrl);

        return insertOmgProductMainImages(image);
    }

    /**
     * 从OSS URL中提取图片信息
     *
     * @param image 图片对象
     * @param ossUrl OSS URL
     */
    private void extractImageInfo(OmgProductMainImages image, String ossUrl) {
        try {
            // 从URL中提取文件名
            String fileName = ossUrl.substring(ossUrl.lastIndexOf('/') + 1);
            if (fileName.contains("?")) {
                fileName = fileName.substring(0, fileName.indexOf("?"));
            }
            image.setImageName(fileName);

            // 提取文件类型
            if (fileName.contains(".")) {
                String fileType = fileName.substring(fileName.lastIndexOf('.') + 1).toLowerCase();
                image.setImageType(fileType);
            }
        } catch (Exception e) {
            log.debug("提取图片信息失败，URL: {}", ossUrl);
        }
    }
}
