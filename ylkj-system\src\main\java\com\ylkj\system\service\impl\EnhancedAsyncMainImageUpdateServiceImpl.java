package com.ylkj.system.service.impl;

import com.ylkj.common.config.UnifiedAsyncConfig;
import com.ylkj.system.mapper.OmgProductsMapper;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.result.MainImageProcessResult;
import com.ylkj.system.service.IAsyncMainImageUpdateService;
import com.ylkj.system.service.IMainImageProcessService;
import com.ylkj.system.service.IMainImageProcessingCacheService;
import com.ylkj.system.service.IOmgProductMainImagesService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 增强版异步主图更新服务实现
 * 支持并行处理、重试机制、缓存等高级功能
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "async.main-image.enhanced", havingValue = "true", matchIfMissing = false)
public class EnhancedAsyncMainImageUpdateServiceImpl implements IAsyncMainImageUpdateService {

    @Resource
    private OmgProductsMapper omgProductsMapper;

    @Resource
    private IMainImageProcessService mainImageProcessService;

    @Resource
    private IOmgProductMainImagesService omgProductMainImagesService;

    @Autowired
    private UnifiedAsyncConfig unifiedAsyncConfig;

    @Resource
    private IMainImageProcessingCacheService mainImageProcessingCacheService;

    // 失败重试队列
    private final Queue<RetryTask> retryQueue = new ConcurrentLinkedQueue<>();

    /**
     * 异步批量更新商品主图
     */
    @Override
    @Async("taskExecutor")
    public void asyncBatchUpdateMainImages(List<OmgProducts> successfulProducts) {
        if (successfulProducts == null || successfulProducts.isEmpty()) {
            log.info("没有需要更新主图的商品");
            return;
        }

        log.info("开始增强版异步批量更新商品主图，商品数量: {}", successfulProducts.size());
        long startTime = System.currentTimeMillis();

        try {
            // 1. 预处理和去重
            List<OmgProducts> processedProducts = preprocessProducts(successfulProducts);

            // 1.1 将处理集批量标记为排队（可观测）
            try {
                Set<String> skuSet = processedProducts.stream()
                        .map(OmgProducts::getSku)
                        .filter(s -> s != null && !s.trim().isEmpty())
                        .collect(Collectors.toCollection(LinkedHashSet::new));
                if (!skuSet.isEmpty()) {
                    mainImageProcessingCacheService.batchMarkAsQueued(skuSet);
                }
            } catch (Exception e) {
                log.warn("批量标记排队失败: {}", e.getMessage());
            }
            
            // 2. 并行处理
            ProcessResult result = processProductsInParallel(processedProducts);
            
            // 3. 🚫 禁用重试队列处理，避免无限重试
            // handleRetryQueue(); // 已禁用，失败时直接跳过
            
            // 4. 输出统计结果
            logFinalResults(successfulProducts.size(), result, startTime);
            
        } catch (Exception e) {
            log.error("增强版异步主图更新处理异常", e);
        } finally {
            // 🆕 清理过期的Redis缓存
            try {
                mainImageProcessingCacheService.cleanExpiredCache();
                log.debug("清理过期的主图处理缓存");
            } catch (Exception e) {
                log.warn("清理过期缓存失败: {}", e.getMessage());
            }
        }
    }

    /**
     * 异步更新单个商品主图
     */
    @Override
    @Async("taskExecutor")
    public void asyncUpdateSingleMainImage(OmgProducts product) {
        if (product == null) {
            log.warn("商品信息为空，跳过主图更新");
            return;
        }

        try {
            ProcessResult result = processSingleProductWithRetry(product);
            log.info("单个商品主图更新完成，SKU: {}, 结果: {}", 
                    product.getSku(), result.isSuccess() ? "成功" : "失败");
        } catch (Exception e) {
            log.error("单个商品主图更新异常，SKU: {}", product.getSku(), e);
        }
    }

    /**
     * 根据平台名称获取对应的mallType
     */
    @Override
    public String getMallTypeByPlatform(String platform) {
        if (platform == null) {
            return null;
        }
        switch (platform.toLowerCase()) {
            case "淘宝":
            case "taobao":
                return "TAOBAO";
            case "1688":
                return "T1688";
            case "weidian":
            case "微店":
                return "WEIDIAN";
            default:
                return null;
        }
    }

    /**
     * 预处理商品列表
     */
    private List<OmgProducts> preprocessProducts(List<OmgProducts> products) {
        // 去重（基于SKU）
        Map<String, OmgProducts> uniqueProducts = new LinkedHashMap<>();
        
        for (OmgProducts product : products) {
            String sku = product.getSku();
            if (sku != null && !sku.trim().isEmpty()) {
                // 如果已存在相同SKU，保留第一个
                uniqueProducts.putIfAbsent(sku, product);
            }
        }
        
        // 过滤有效商品
        List<OmgProducts> validProducts = uniqueProducts.values().stream()
                .filter(this::isValidProduct)
                .collect(Collectors.toList());
        
        log.info("预处理完成，原始数量: {}, 去重后: {}, 有效商品: {}", 
                products.size(), uniqueProducts.size(), validProducts.size());
        
        return validProducts;
    }

    /**
     * 检查商品是否有效
     * 增加数据库预检查，避免重复处理已有主图的商品
     */
    private boolean isValidProduct(OmgProducts product) {
        String sku = product.getSku();
        String platform = product.getPlatform();
        
        // 1. 检查SKU有效性
        if (sku == null || sku.trim().isEmpty()) {
            return false;
        }
        
        // 2. 检查平台有效性
        if (platform == null || platform.trim().isEmpty()) {
            return false;
        }
        
        // 3. 检查平台支持
        if (getMallTypeByPlatform(platform) == null) {
            return false;
        }
        
        // 4. 🆕 数据库预检查：是否已有主图记录
        try {
            int existingCount = omgProductMainImagesService.countImagesBySku(sku);
            if (existingCount > 0) {
                log.debug("SKU: {} 数据库已有{}条主图记录，跳过处理", sku, existingCount);
                return false;
            }
        } catch (Exception e) {
            log.warn("检查SKU {} 主图记录失败: {}", sku, e.getMessage());
            // 检查失败时保守处理：仍然处理该商品
            return true;
        }
        
        // 5. 🆕 缓存状态检查：是否正在处理中
        if (mainImageProcessingCacheService.isProcessing(sku)) {
            log.debug("SKU: {} 正在处理中，跳过", sku);
            return false;
        }
        
        // 6. 🆕 缓存状态检查：是否最近失败过
        if (mainImageProcessingCacheService.isRecentlyFailed(sku)) {
            log.debug("SKU: {} 最近处理失败，跳过", sku);
            return false;
        }
        
        return true;
    }

    /**
     * 并行处理商品
     */
    private ProcessResult processProductsInParallel(List<OmgProducts> products) {
        if (!unifiedAsyncConfig.getMainImage().isEnableParallel() || products.size() <= unifiedAsyncConfig.getMainImage().getBatchSize()) {
            // 串行处理
            return processProductsSequentially(products);
        }

        // 并行处理
        ExecutorService executor = Executors.newFixedThreadPool(unifiedAsyncConfig.getMainImage().getThreadPoolSize());
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);
        
        try {
            List<List<OmgProducts>> batches = partitionList(products, unifiedAsyncConfig.getMainImage().getBatchSize());
            List<CompletableFuture<Void>> futures = new ArrayList<>();
            
            for (List<OmgProducts> batch : batches) {
                CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                    ProcessResult batchResult = processBatch(batch);
                    successCount.addAndGet(batchResult.getSuccessCount());
                    failedCount.addAndGet(batchResult.getFailedCount());
                }, executor);
                
                futures.add(future);
            }
            
            // 等待所有批次完成
            CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                    .get(unifiedAsyncConfig.getMainImage().getTimeoutSeconds(), TimeUnit.SECONDS);
            
        } catch (TimeoutException e) {
            log.error("并行处理超时", e);
        } catch (Exception e) {
            log.error("并行处理异常", e);
        } finally {
            executor.shutdown();
        }
        
        return new ProcessResult(successCount.get(), failedCount.get());
    }

    /**
     * 串行处理商品
     */
    private ProcessResult processProductsSequentially(List<OmgProducts> products) {
        int successCount = 0;
        int failedCount = 0;
        
        for (OmgProducts product : products) {
            ProcessResult result = processSingleProductWithRetry(product);
            if (result.isSuccess()) {
                successCount++;
            } else {
                failedCount++;
            }
        }
        
        return new ProcessResult(successCount, failedCount);
    }

    /**
     * 处理单个批次
     */
    private ProcessResult processBatch(List<OmgProducts> batch) {
        int successCount = 0;
        int failedCount = 0;
        
        for (OmgProducts product : batch) {
            ProcessResult result = processSingleProductWithRetry(product);
            if (result.isSuccess()) {
                successCount++;
            } else {
                failedCount++;
            }
            
            // 添加延迟
                    if (unifiedAsyncConfig.getMainImage().getRequestDelay() > 0) {
            try {
                Thread.sleep(unifiedAsyncConfig.getMainImage().getRequestDelay());
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
        
        return new ProcessResult(successCount, failedCount);
    }

    /**
     * 处理单个商品（禁用重试，失败时直接跳过）
     */
    private ProcessResult processSingleProductWithRetry(OmgProducts product) {
        String sku = product.getSku();
        
        // 🆕 检查Redis缓存状态
        if (mainImageProcessingCacheService.isProcessing(sku)) {
            log.debug("SKU: {} 正在处理中，跳过", sku);
            return new ProcessResult(0, 0);
        }
        
        if (mainImageProcessingCacheService.isRecentlyFailed(sku)) {
            log.debug("SKU: {} 最近处理失败，跳过", sku);
            return new ProcessResult(0, 1);
        }
        
        // 🆕 从队列提升为处理中（若已不在队列也允许直接占用）
        if (mainImageProcessingCacheService.isQueued(sku)) {
            if (!mainImageProcessingCacheService.promoteQueuedToProcessing(sku)) {
                log.debug("SKU: {} 无法从队列提升为处理中，跳过", sku);
                return new ProcessResult(0, 0);
            }
        } else {
            if (!mainImageProcessingCacheService.markAsProcessing(sku)) {
                log.debug("SKU: {} 无法标记为处理中，可能被其他请求抢占，跳过", sku);
                return new ProcessResult(0, 0);
            }
        }
        
        // 🆕 单次处理，不重试
        try {
            String mallType = getMallTypeByPlatform(product.getPlatform());
            MainImageProcessResult result =
                    mainImageProcessService.processProductMainImage(product, mallType);
            
            if (result.isSuccess() && result.getMainImageUrl() != null) {
                updateProductMainImage(product, result.getMainImageUrl());
                log.debug("SKU: {} 主图更新成功", sku);
                // 🆕 标记处理完成
                mainImageProcessingCacheService.markAsCompleted(sku);
                return new ProcessResult(1, 0);
            } else {
                // ✅ 失败时直接跳过，设为空数据，无需重试
                log.info("SKU: {} 主图获取失败，原因: {}，直接跳过不重试", sku, 
                         result != null ? result.getMessage() : "未知错误");
                // 🆕 标记处理失败
                mainImageProcessingCacheService.markAsFailed(sku);
                return new ProcessResult(0, 1);
            }
            
        } catch (Exception e) {
            // ✅ 异常时也直接跳过，不重试
            log.warn("SKU: {} 主图处理异常: {}，直接跳过不重试", sku, e.getMessage());
            
            // 🆕 标记处理失败
            mainImageProcessingCacheService.markAsFailed(sku);
            return new ProcessResult(0, 1);
        }
    }

    /**
     * 更新商品主图
     */
    private void updateProductMainImage(OmgProducts product, String mainImageUrl) {
        try {
            // product.setMainImage(mainImageUrl);
            omgProductsMapper.updateOmgProducts(product);
        } catch (Exception e) {
            log.error("更新商品主图失败，SKU: {}", product.getSku(), e);
            throw e;
        }
    }

    /**
     * 处理重试队列
     * 🚫 已禁用：为避免无限重试问题，失败时直接跳过不重试
     */
    @Deprecated
    private void handleRetryQueue() {
        // 🚫 重试队列处理已禁用，避免无限重试循环
        log.debug("重试队列处理已禁用，当前队列大小: {}", retryQueue.size());
        
        // 清空重试队列，释放内存
        if (!retryQueue.isEmpty()) {
            int queueSize = retryQueue.size();
            retryQueue.clear();
            log.info("已清空重试队列，避免内存占用，清空数量: {}", queueSize);
        }
    }

    /**
     * 输出最终结果
     */
    private void logFinalResults(int totalCount, ProcessResult result, long startTime) {
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        
        log.info("增强版异步批量更新商品主图完成");
        log.info("总商品数: {}, 成功: {}, 失败: {}, 耗时: {}ms", 
                totalCount, result.getSuccessCount(), result.getFailedCount(), duration);
        log.info("平均处理时间: {}ms/商品", totalCount > 0 ? duration / totalCount : 0);
    }

    /**
     * 分割列表
     */
    private <T> List<List<T>> partitionList(List<T> list, int batchSize) {
        List<List<T>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += batchSize) {
            partitions.add(list.subList(i, Math.min(i + batchSize, list.size())));
        }
        return partitions;
    }

    /**
     * 处理结果类
     */
    private static class ProcessResult {
        private final int successCount;
        private final int failedCount;
        
        public ProcessResult(int successCount, int failedCount) {
            this.successCount = successCount;
            this.failedCount = failedCount;
        }
        
        public int getSuccessCount() { return successCount; }
        public int getFailedCount() { return failedCount; }
        public boolean isSuccess() { return successCount > 0 && failedCount == 0; }
    }

    /**
     * 重试任务类
     */
    private static class RetryTask {
        private final OmgProducts product;
        private final long timestamp;
        
        public RetryTask(OmgProducts product, long timestamp) {
            this.product = product;
            this.timestamp = timestamp;
        }
        
        public OmgProducts getProduct() { return product; }
        public long getTimestamp() { return timestamp; }
    }
}
