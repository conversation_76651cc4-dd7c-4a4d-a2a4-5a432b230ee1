<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.system.mapper.SOmgCategoriesMapper">
    
    <resultMap type="SOmgCategories" id="OmgCategoriesResult">
        <result property="categoryId"    column="category_id"    />
        <result property="name"    column="name"    />
        <result property="slug"    column="slug"    />
        <result property="description"    column="description"    />
        <result property="imageUrl"    column="image_url"    />
        <result property="displayOrder"    column="display_order"    />
        <result property="createdAt"    column="created_at"    />
        <result property="updatedAt"    column="updated_at"    />
    </resultMap>

    <sql id="selectOmgCategoriesVo">
        select category_id, name, slug, description, image_url, display_order, created_at, updated_at from omg_categories
    </sql>

    <select id="selectOmgCategoriesList" parameterType="OmgCategories" resultMap="OmgCategoriesResult">
        <include refid="selectOmgCategoriesVo"/>
        <where>  
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="slug != null  and slug != ''"> and slug like concat('%', #{slug}, '%')</if>
        </where>
    </select>
    
    <select id="selectOmgCategoriesByCategoryId" parameterType="Long" resultMap="OmgCategoriesResult">
        <include refid="selectOmgCategoriesVo"/>
        where category_id = #{categoryId}
    </select>
    <select id="selectCategoryIdByName" resultType="java.lang.Long" parameterType="java.lang.String">
        select category_id from omg_categories where name = #{categoryName}
    </select>

    <!-- 批量查询分类ID映射 -->
    <select id="batchSelectCategoryIdsByNames" resultType="com.ylkj.system.model.dto.CategoryNameIdMapping">
        SELECT name as categoryName, category_id as categoryId
        FROM omg_categories
        WHERE name IN
        <foreach collection="categoryNames" item="name" open="(" separator="," close=")">
            #{name}
        </foreach>
    </select>

    <!-- 批量插入分类 -->
    <insert id="batchInsertCategoriesIgnore" parameterType="java.util.List">
        INSERT IGNORE INTO omg_categories (name, created_at, updated_at)
        VALUES
        <foreach collection="categories" item="category" separator=",">
            (#{category.name}, NOW(), NOW())
        </foreach>
    </insert>

    <insert id="insertOmgCategories" parameterType="OmgCategories" useGeneratedKeys="true" keyProperty="categoryId">
        insert into omg_categories
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="slug != null and slug != ''">slug,</if>
            <if test="description != null">description,</if>
            <if test="imageUrl != null">image_url,</if>
            <if test="displayOrder != null">display_order,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="updatedAt != null">updated_at,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="slug != null and slug != ''">#{slug},</if>
            <if test="description != null">#{description},</if>
            <if test="imageUrl != null">#{imageUrl},</if>
            <if test="displayOrder != null">#{displayOrder},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="updatedAt != null">#{updatedAt},</if>
         </trim>
    </insert>

    <update id="updateOmgCategories" parameterType="OmgCategories">
        update omg_categories
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="slug != null and slug != ''">slug = #{slug},</if>
            <if test="description != null">description = #{description},</if>
            <if test="imageUrl != null">image_url = #{imageUrl},</if>
            <if test="displayOrder != null">display_order = #{displayOrder},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="updatedAt != null">updated_at = #{updatedAt},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteOmgCategoriesByCategoryId" parameterType="Long">
        delete from omg_categories where category_id = #{categoryId}
    </delete>

    <delete id="deleteOmgCategoriesByCategoryIds" parameterType="String">
        delete from omg_categories where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>
</mapper>