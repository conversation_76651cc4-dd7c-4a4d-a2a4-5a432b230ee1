package com.ylkj.service;


import com.ylkj.model.domain.CnfansProductInfo;
import com.ylkj.model.domain.SyncResult;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.service.IOmgProductsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * CNFans数据同步服务
 * 用于将CNFans的商品信息同步到OMG商品表
 */
@Slf4j
@Service
public class CnfansDataSyncService {

    @Autowired
    private CnfansApiService cnfansApiService;

    @Autowired
    @Qualifier("omgProductsServiceImpl")
    private IOmgProductsService omgProductsService;

    /**
     * 根据SKU同步单个商品的CNFans数据
     * 
     * @param sku 商品SKU
     * @return 是否同步成功
     */
    public boolean syncProductBySku(String sku) {
        try {
            log.info("开始同步商品CNFans数据，SKU: {}", sku);

            // 1. 根据SKU查询商品
            OmgProducts product = omgProductsService.selectOmgProductsBySku(sku);
            if (product == null) {
                log.warn("未找到SKU为{}的商品", sku);
                return false;
            }

            // 2. 调用CNFans API获取数据
            CnfansProductInfo cnfansInfo = cnfansApiService.getProductInfo(sku);
            if (cnfansInfo == null) {
                log.warn("未能从CNFans获取SKU为{}的商品信息", sku);
                return false;
            }

            // 3. 更新商品信息
            boolean updated = updateProductWithCnfansData(product, cnfansInfo);
            
            if (updated) {
                log.info("成功同步商品CNFans数据，SKU: {}, 运输天数: {}, 重量: {}", 
                        sku, cnfansInfo.getAverageArrival(), cnfansInfo.getWeight());
                return true;
            } else {
                log.warn("同步商品CNFans数据失败，SKU: {}", sku);
                return false;
            }

        } catch (Exception e) {
            log.error("同步商品CNFans数据异常，SKU: {}", sku, e);
            return false;
        }
    }

    /**
     * 批量同步商品的CNFans数据
     * 
     * @param skuList SKU列表
     * @return 同步结果统计
     */
    public SyncResult batchSyncProducts(List<String> skuList) {
        SyncResult result = new SyncResult();
        result.setTotal(skuList.size());

        for (String sku : skuList) {
            try {
                boolean success = syncProductBySku(sku);
                if (success) {
                    result.incrementSuccess();
                } else {
                    result.incrementFailed();
                }

                // 添加延迟避免请求过于频繁
                Thread.sleep(1000);

            } catch (Exception e) {
                log.error("批量同步商品异常，SKU: {}", sku, e);
                result.incrementFailed();
            }
        }

        log.info("批量同步完成，总数: {}, 成功: {}, 失败: {}", 
                result.getTotal(), result.getSuccess(), result.getFailed());

        return result;
    }

    /**
     * 同步所有商品的CNFans数据
     * 
     * @return 同步结果统计
     */
    public SyncResult syncAllProducts() {
        try {
            log.info("开始同步所有商品的CNFans数据");

            // 获取所有商品的SKU列表
            List<String> allSkus = omgProductsService.getAllProductSkus();
            
            if (allSkus.isEmpty()) {
                log.warn("没有找到任何商品SKU");
                return new SyncResult();
            }

            log.info("找到{}个商品SKU，开始批量同步", allSkus.size());
            return batchSyncProducts(allSkus);

        } catch (Exception e) {
            log.error("同步所有商品CNFans数据异常", e);
            SyncResult result = new SyncResult();
            result.setTotal(0);
            return result;
        }
    }

    /**
     * 使用CNFans数据更新商品信息
     * 
     * @param product 商品对象
     * @param cnfansInfo CNFans信息
     * @return 是否更新成功
     */
    private boolean updateProductWithCnfansData(OmgProducts product, CnfansProductInfo cnfansInfo) {
        try {
            boolean needUpdate = false;

            // 更新运输天数
            if (StringUtils.hasText(cnfansInfo.getAverageArrival())) {
                product.setAverageArrival(cnfansInfo.getAverageArrival());
                needUpdate = true;
            }

            // 更新重量
            if (StringUtils.hasText(cnfansInfo.getWeight())) {
                try {
                    BigDecimal weight = new BigDecimal(cnfansInfo.getWeight());
                    product.setWeight(weight);
                    needUpdate = true;
                } catch (NumberFormatException e) {
                    log.warn("重量格式不正确，SKU: {}, weight: {}", product.getSku(), cnfansInfo.getWeight());
                }
            }

            // 如果有数据需要更新，则执行更新操作
            if (needUpdate) {
                int updateResult = omgProductsService.updateOmgProducts(product);
                return updateResult > 0;
            }

            return true;

        } catch (Exception e) {
            log.error("更新商品CNFans数据异常，SKU: {}", product.getSku(), e);
            return false;
        }
    }

}
