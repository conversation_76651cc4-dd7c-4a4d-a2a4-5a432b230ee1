package com.ylkj.system.service.impl;

import com.ylkj.system.mapper.OmgBrandsMapper;
import com.ylkj.system.mapper.SOmgCategoriesMapper;
import com.ylkj.system.model.domain.OmgBrands;
import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.domain.SOmgCategories;
import com.ylkj.system.model.dto.BrandNameIdMapping;
import com.ylkj.system.model.dto.BrandQueryRequest;
import com.ylkj.system.model.dto.CategoryNameIdMapping;
import com.ylkj.system.service.CategoryBrandBatchService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 分类和品牌批量处理服务实现类
 * 提供高性能的批量分类和品牌处理功能
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Service
public class CategoryBrandBatchServiceImpl implements CategoryBrandBatchService {
    
    private static final Logger log = LoggerFactory.getLogger(CategoryBrandBatchServiceImpl.class);
    
    @Autowired
    private SOmgCategoriesMapper sOmgCategoriesMapper;
    
    @Autowired
    private OmgBrandsMapper omgBrandsMapper;
    
    // 性能统计参数
    private long totalProcessTime = 0;
    private int totalCategoriesProcessed = 0;
    private int totalBrandsProcessed = 0;
    private int batchCount = 0;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void processCategoriesAndBrandsBatch(List<OmgProducts> products) {
        if (products == null || products.isEmpty()) {
            log.warn("商品列表为空，跳过批量处理");
            return;
        }
        
        long startTime = System.currentTimeMillis();
        batchCount++;
        
        log.info("开始批量处理分类和品牌，商品数量: {}", products.size());

        // 调试：打印前几个商品的分类信息
        System.out.println("=== 商品分类信息调试 ===");
        for (int i = 0; i < Math.min(5, products.size()); i++) {
            OmgProducts product = products.get(i);
            System.out.println("商品" + (i+1) + ": " + product.getName());
            System.out.println("  - categoryName: '" + product.getCategoryName() + "'");
            System.out.println("  - secondCategoryName: '" + product.getSecondCategoryName() + "'");
            System.out.println("  - categoryId: " + product.getCategoryId());
            System.out.println("  - brandId: " + product.getBrandId());
        }

        try {
            // 1. 收集所有唯一的分类名称
            Set<String> uniqueCategoryNames = products.stream()
                    .map(OmgProducts::getCategoryName)
                    .filter(Objects::nonNull)
                    .filter(name -> !name.trim().isEmpty())
                    .collect(Collectors.toSet());

            System.out.println("=== 收集到的分类信息 ===");
            System.out.println("唯一分类数量: " + uniqueCategoryNames.size());
            System.out.println("分类名称: " + uniqueCategoryNames);

            // 2. 批量处理分类
            Map<String, Long> categoryMapping = new HashMap<>();
            if (!uniqueCategoryNames.isEmpty()) {
                log.info("处理分类数量: {}", uniqueCategoryNames.size());
                categoryMapping = batchGetOrCreateCategoryIds(new ArrayList<>(uniqueCategoryNames));
                totalCategoriesProcessed += uniqueCategoryNames.size();
            } else {
                System.out.println("没有找到任何分类信息！");
            }
            
            // 3. 为商品设置分类ID
            System.out.println("=== 为商品设置分类ID ===");
            for (OmgProducts product : products) {
                if (product.getCategoryName() != null && !product.getCategoryName().trim().isEmpty()) {
                    Long categoryId = categoryMapping.get(product.getCategoryName());
                    product.setCategoryId(categoryId);
                    System.out.println("商品: " + product.getName() + ", 分类: " + product.getCategoryName() + " -> ID: " + categoryId);
                }
            }

            // 4. 收集所有唯一的品牌请求（品牌名称+分类ID）
            List<BrandQueryRequest> uniqueBrandRequests = products.stream()
                    .filter(p -> p.getSecondCategoryName() != null && !p.getSecondCategoryName().trim().isEmpty())
                    .filter(p -> p.getCategoryId() != null)
                    .map(p -> new BrandQueryRequest(p.getSecondCategoryName(), p.getCategoryId()))
                    .distinct()
                    .collect(Collectors.toList());

            System.out.println("=== 收集到的品牌信息 ===");
            System.out.println("唯一品牌数量: " + uniqueBrandRequests.size());
            System.out.println("品牌请求: " + uniqueBrandRequests);
            
            // 5. 批量处理品牌
            Map<String, Long> brandMapping = new HashMap<>();
            if (!uniqueBrandRequests.isEmpty()) {
                log.debug("处理品牌数量: {}", uniqueBrandRequests.size());
                brandMapping = batchGetOrCreateBrandIds(uniqueBrandRequests);
                totalBrandsProcessed += uniqueBrandRequests.size();
            }
            
            // 6. 为商品设置品牌ID和处理价格
            for (OmgProducts product : products) {
                // 设置品牌ID
                if (product.getSecondCategoryName() != null && !product.getSecondCategoryName().trim().isEmpty() 
                    && product.getCategoryId() != null) {
                    String brandKey = product.getSecondCategoryName() + "_" + product.getCategoryId();
                    Long brandId = brandMapping.get(brandKey);
                    product.setBrandId(brandId);
                }
                
                // 处理价格
                if (product.getPrice() != null) {
                    product.setPrice(product.getPrice().divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP));
                }
                if (product.getOriginalPrice() != null) {
                    product.setOriginalPrice(product.getOriginalPrice().divide(new BigDecimal("1"), 2, RoundingMode.HALF_UP));
                }
            }
            
            long endTime = System.currentTimeMillis();
            totalProcessTime += (endTime - startTime);
            
            log.info("批量处理完成，耗时: {}ms, 处理分类: {}, 处理品牌: {}", 
                    (endTime - startTime), uniqueCategoryNames.size(), uniqueBrandRequests.size());
            
        } catch (Exception e) {
            log.error("批量处理分类和品牌失败", e);
            throw e;
        }
    }
    
    @Override
    public Map<String, Long> batchGetOrCreateCategoryIds(List<String> categoryNames) {
        return batchProcessCategoriesWithInsertIgnore(categoryNames);
    }

    @Override
    public Map<String, Long> batchGetOrCreateBrandIds(List<BrandQueryRequest> brandRequests) {
        return batchProcessBrandsWithInsertIgnore(brandRequests);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    public Map<String, Long> batchProcessCategoriesWithInsertIgnore(List<String> categoryNames) {
        if (categoryNames == null || categoryNames.isEmpty()) {
            return new HashMap<>();
        }

        try {
            log.info("开始批量处理分类，数量: {}", categoryNames.size());
            System.out.println("开始批量处理分类，数量: " + categoryNames.size());
            System.out.println("分类名称列表: " + categoryNames);

            // 1. 先查询已存在的分类
            List<CategoryNameIdMapping> existingMappings = sOmgCategoriesMapper.batchSelectCategoryIdsByNames(categoryNames);
            Map<String, Long> existingCategoryMap = existingMappings.stream()
                    .collect(Collectors.toMap(
                            CategoryNameIdMapping::getCategoryName,
                            CategoryNameIdMapping::getCategoryId,
                            (existing, replacement) -> existing
                    ));

            System.out.println("数据库中已存在的分类: " + existingCategoryMap.keySet());

            // 2. 找出需要新增的分类
            List<String> newCategoryNames = categoryNames.stream()
                    .filter(name -> !existingCategoryMap.containsKey(name))
                    .collect(Collectors.toList());

            System.out.println("需要新增的分类: " + newCategoryNames);

            // 3. 批量插入新分类
            int insertCount = 0;
            if (!newCategoryNames.isEmpty()) {
                List<SOmgCategories> categoriesToInsert = newCategoryNames.stream()
                        .map(name -> {
                            SOmgCategories category = new SOmgCategories();
                            category.setName(name);
                            return category;
                        })
                        .collect(Collectors.toList());

                insertCount = sOmgCategoriesMapper.batchInsertCategoriesIgnore(categoriesToInsert);
                log.info("批量插入新分类完成，新增: {} 个", insertCount);
                System.out.println("批量插入新分类完成，新增: " + insertCount + " 个");
            } else {
                System.out.println("没有需要新增的分类");
            }

            // 4. 重新查询所有分类ID（包括新插入的）
            List<CategoryNameIdMapping> allMappings = sOmgCategoriesMapper.batchSelectCategoryIdsByNames(categoryNames);
            System.out.println("查询到的分类映射数量: " + allMappings.size());
            System.out.println("分类映射详情: " + allMappings);

            // 5. 转换为Map
            Map<String, Long> result = allMappings.stream()
                    .collect(Collectors.toMap(
                            CategoryNameIdMapping::getCategoryName,
                            CategoryNameIdMapping::getCategoryId,
                            (existing, replacement) -> existing
                    ));

            log.info("批量处理分类完成，获取到: {} 个映射，新增: {} 个", result.size(), insertCount);
            System.out.println("最终分类映射结果: " + result);
            return result;

        } catch (Exception e) {
            log.error("批量处理分类失败，错误详情: {}", e.getMessage(), e);
            System.err.println("批量处理分类失败，错误详情: " + e.getMessage());
            e.printStackTrace();
            // 回退到原有逐个处理方式
            System.out.println("回退到逐个处理分类...");
            return fallbackProcessCategories(categoryNames);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    public Map<String, Long> batchProcessBrandsWithInsertIgnore(List<BrandQueryRequest> brandRequests) {
        if (brandRequests == null || brandRequests.isEmpty()) {
            return new HashMap<>();
        }

        try {
            log.info("开始批量处理品牌，数量: {}", brandRequests.size());
            System.out.println("开始批量处理品牌，数量: " + brandRequests.size());
            System.out.println("品牌请求列表: " + brandRequests);

            // 1. 先查询已存在的品牌
            List<BrandNameIdMapping> existingMappings = omgBrandsMapper.batchSelectBrandIdsByNames(brandRequests);
            Map<String, Long> existingBrandMap = existingMappings.stream()
                    .collect(Collectors.toMap(
                            BrandNameIdMapping::getBrandKey,
                            BrandNameIdMapping::getBrandId,
                            (existing, replacement) -> existing
                    ));

            System.out.println("数据库中已存在的品牌: " + existingBrandMap.keySet());

            // 2. 找出需要新增的品牌
            List<BrandQueryRequest> newBrandRequests = brandRequests.stream()
                    .filter(request -> {
                        String brandKey = request.getBrandName() + "_" + request.getCategoryId();
                        return !existingBrandMap.containsKey(brandKey);
                    })
                    .collect(Collectors.toList());

            System.out.println("需要新增的品牌: " + newBrandRequests);

            // 3. 批量插入新品牌
            int insertCount = 0;
            if (!newBrandRequests.isEmpty()) {
                List<OmgBrands> brandsToInsert = newBrandRequests.stream()
                        .map(request -> {
                            OmgBrands brand = new OmgBrands();
                            brand.setName(request.getBrandName());
                            brand.setParentCategoryId(request.getCategoryId());
                            return brand;
                        })
                        .collect(Collectors.toList());

                insertCount = omgBrandsMapper.batchInsertBrandsIgnore(brandsToInsert);
                log.info("批量插入新品牌完成，新增: {} 个", insertCount);
                System.out.println("批量插入新品牌完成，新增: " + insertCount + " 个");
            } else {
                System.out.println("没有需要新增的品牌");
            }

            // 4. 重新查询所有品牌ID（包括新插入的）
            List<BrandNameIdMapping> allMappings = omgBrandsMapper.batchSelectBrandIdsByNames(brandRequests);
            System.out.println("查询到的品牌映射数量: " + allMappings.size());
            System.out.println("品牌映射详情: " + allMappings);

            // 5. 转换为Map（使用品牌键：品牌名称_分类ID）
            Map<String, Long> result = allMappings.stream()
                    .collect(Collectors.toMap(
                            BrandNameIdMapping::getBrandKey,
                            BrandNameIdMapping::getBrandId,
                            (existing, replacement) -> existing
                    ));

            log.info("批量处理品牌完成，获取到: {} 个映射，新增: {} 个", result.size(), insertCount);
            System.out.println("最终品牌映射结果: " + result);
            return result;

        } catch (Exception e) {
            log.error("批量处理品牌失败，错误详情: {}", e.getMessage(), e);
            System.err.println("批量处理品牌失败，错误详情: " + e.getMessage());
            e.printStackTrace();
            // 回退到原有逐个处理方式
            System.out.println("回退到逐个处理品牌...");
            return fallbackProcessBrands(brandRequests);
        }
    }

    @Override
    public String getPerformanceStats() {
        if (batchCount == 0) {
            return "暂无批量处理统计信息";
        }

        double avgProcessTime = (double) totalProcessTime / batchCount;
        return String.format("批量处理统计 - 总批次: %d, 总耗时: %dms, 平均耗时: %.2fms, " +
                "处理分类总数: %d, 处理品牌总数: %d",
                batchCount, totalProcessTime, avgProcessTime,
                totalCategoriesProcessed, totalBrandsProcessed);
    }

    @Override
    public void clearCacheAndStats() {
        totalProcessTime = 0;
        totalCategoriesProcessed = 0;
        totalBrandsProcessed = 0;
        batchCount = 0;
        log.info("已清理缓存和统计信息");
    }

    /**
     * 回退方案：逐个处理分类
     * 当批量处理失败时使用
     */
    private Map<String, Long> fallbackProcessCategories(List<String> categoryNames) {
        log.warn("使用回退方案逐个处理分类");
        Map<String, Long> categoryMapping = new HashMap<>();

        for (String categoryName : categoryNames) {
            try {
                Long categoryId = sOmgCategoriesMapper.selectCategoryIdByName(categoryName);
                if (categoryId == null) {
                    SOmgCategories category = new SOmgCategories();
                    category.setName(categoryName);
                    sOmgCategoriesMapper.insertOmgCategories(category);
                    categoryId = category.getCategoryId();
                }
                categoryMapping.put(categoryName, categoryId);
            } catch (Exception e) {
                log.error("处理分类失败: {}", categoryName, e);
            }
        }

        return categoryMapping;
    }

    /**
     * 回退方案：逐个处理品牌
     * 当批量处理失败时使用
     */
    private Map<String, Long> fallbackProcessBrands(List<BrandQueryRequest> brandRequests) {
        log.warn("使用回退方案逐个处理品牌");
        Map<String, Long> brandMapping = new HashMap<>();

        for (BrandQueryRequest request : brandRequests) {
            try {
                Long brandId = omgBrandsMapper.selectBrandIdByName(request.getBrandName(), request.getCategoryId());
                if (brandId == null) {
                    OmgBrands brand = new OmgBrands();
                    brand.setName(request.getBrandName());
                    brand.setParentCategoryId(request.getCategoryId());
                    omgBrandsMapper.insertOmgBrands(brand);
                    brandId = brand.getBrandId();
                }
                String brandKey = request.getBrandName() + "_" + request.getCategoryId();
                brandMapping.put(brandKey, brandId);
            } catch (Exception e) {
                log.error("处理品牌失败: {}, 分类ID: {}", request.getBrandName(), request.getCategoryId(), e);
            }
        }

        return brandMapping;
    }

}
