package com.ylkj.mapper.omg;

import com.ylkj.model.domain.Products;
import com.ylkj.model.domain.omg.OProducts;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ylkj.model.vo.OProductsVO;
import com.ylkj.model.vo.OSkuProductsVO;
import com.ylkj.model.vo.ProductsVo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * omg_商品表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-15
 */
public interface OProductsMapper extends BaseMapper<OProducts> {

    /**
     * 关键词搜索商品
     *
     * @param keyword 搜索关键词
     * @param category 分类名称或ID或slug
     * @param minPrice 最低价格
     * @param maxPrice 最高价格
     * @return 搜索结果商品列表
     */
    List<OProducts> searchProducts(
            @Param("keyword") String keyword,
            @Param("category") String category,
            @Param("minPrice") BigDecimal minPrice,
            @Param("maxPrice") BigDecimal maxPrice
    );

    /**
     * 根据分类ID和品牌id获取商品列表
     * @param categoryId 分类ID品牌id
     * @return 商品列表
     */
    List<OProducts> getProductsByCategory(@Param("categoryId") Long categoryId,
                                          @Param("brandId") Long brandId,@Param("merchant")String merchant);
    /**
     * @Author: 吴居乐
     * @Description: 根据商品id获取商品详情
     * @DateTime: 20:39 2025/4/23/周三
     * @Params:
     * @Return
     */
    OProductsVO getProductById(@Param("productId") long productId);

    /**
    * @Author: 吴居乐
    * @Description: 更新游览量
    * @DateTime: 11:02 2025/7/8/周二
    * @Params: [productId]
    * @Return int
    */
    int updateViews(@Param("productId") Long productId);

    /**
    * @Author: 吴居乐
    * @Description: 获取推荐商品
    * @DateTime: 11:03 2025/7/8/周二
    * @Params: [categoryId, brandId]
    * @Return java.util.List<com.ylkj.model.domain.omg.OProducts>
    */
    List<OProducts> getRecommendProducts(@Param("categoryId") Integer categoryId,@Param("brandId") Long brandId);

    /**
    * @Author: 吴居乐
    * @Description: 获取sku商品
    * @DateTime: 11:03 2025/7/8/周二
    * @Params: [id]
    * @Return java.util.List<com.ylkj.model.vo.OSkuProductsVO>
    */
    List<OSkuProductsVO> getSkuProducts(String id);

    /**
    * @Author: 吴居乐
    * @Description: 点赞商品
    * @DateTime: 11:04 2025/7/8/周二
    * @Params: [productId]
    * @Return int
    */
    int likeProduct(Long productId);

    /**
    * @Author: 吴居乐
    * @Description: 取消点赞商品
    * @DateTime: 11:04 2025/7/8/周二
    * @Params: [productId]
    * @Return int
    */
    int unLikeProduct(Long productId);

    /**
     * @author: 小许
     * @date: 2025/7/9/周三 11:24
     * @description: 获取所有商家名称
     * @return List<String>
     */
    List<String> getAllMerchant();
    
    /**
     * 获取所有商品基本信息（不包括qc_images）
     * 
     * @return 商品列表
     */
    @Select("SELECT product_id, seller_id, category_id, name, slug, description, price, original_price, " +
            "main_image, sku, stock, likes, views, rating, total_ratings, status, created_at, " +
            "updated_at, merchant, platform, product_status, qc, average_arrival, weight, product_attributes FROM omg_products")
    List<OProducts> selectAllProductsBasicInfo();
    
    /**
     * 分页查询商品基本信息（不包括qc_images）
     * 
     * @param page 分页参数
     * @return 分页商品列表
     */
    @Select("SELECT product_id, seller_id, category_id, name, slug, description, price, original_price, " +
            "main_image, sku, stock, likes, views, rating, total_ratings, status, created_at, " +
            "updated_at, merchant, platform, product_status, qc, product_attributes FROM omg_products")
    com.baomidou.mybatisplus.extension.plugins.pagination.Page<OProducts> selectPageBasicInfo(
            com.baomidou.mybatisplus.extension.plugins.pagination.Page<OProducts> page);
    
    /**
     * 根据SKU查询商品基本信息（不包括qc_images）
     * 
     * @param sku 商品SKU
     * @return 商品列表
     */
    @Select("SELECT product_id, seller_id, category_id, name, slug, description, price, original_price, " +
            "main_image, sku, stock, likes, views, rating, total_ratings, status, created_at, " +
            "updated_at, merchant, platform, product_status, qc, average_arrival, weight, product_attributes FROM omg_products WHERE sku = #{sku}")
    List<OProducts> selectBySkuBasicInfo(@Param("sku") String sku);


    /**
     * 根据商品状态查询商品基本信息（不包括qc_images）
     *
     * @param statusList 商品状态列表
     * @return 商品列表
     */
    List<OProducts> getProductsByStatus(@Param("statusList") List<Integer> statusList);

    List<OProducts> getAllRecommendProducts();

    /**
     * 批量更新商品QC数量
     *
     * @param productList 要更新的商品列表
     * @return 更新成功的记录数
     */
    int batchUpdateProductsQc(@Param("list") List<OProducts> productList);

    /**
     * 根据SKU批量更新商品QC数量
     *
     * @param skuQcMap SKU和QC数量的映射
     * @return 更新成功的记录数
     */
    int batchUpdateProductsQcBySku(@Param("skuQcMap") java.util.Map<String, String> skuQcMap);

    /**
     * 根据SKU更新单个商品的QC数量
     *
     * @param sku SKU
     * @param qc QC数量
     * @return 更新成功的记录数
     */
    int updateProductQcBySku(@Param("sku") String sku, @Param("qc") String qc);
}
