package com.ylkj.system.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务配置
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
@Configuration
@EnableAsync
public class AsyncConfig {
    
    /**
     * 异步任务线程池 (使用Spring Boot默认配置)
     * 线程池参数由 application-async-unified.yml 中的 spring.task.execution.pool 配置
     */
    @Bean("taskExecutor")
    public Executor taskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 使用默认配置，由Spring Boot自动配置
        // 核心线程数: 3 (来自配置文件)
        // 最大线程数: 6 (来自配置文件) 
        // 队列容量: 150 (来自配置文件)
        // 线程名前缀: "async-unified-" (来自配置文件)
        
        // 保持原有的拒绝策略和优雅关闭设置
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(60);
        
        executor.initialize();
        return executor;
    }
    
    /**
     * 导入专用线程池（用于CPU密集型任务）
     */
    @Bean("importExecutor")
    public Executor importExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 根据CPU核心数设置
        int corePoolSize = Runtime.getRuntime().availableProcessors();
        executor.setCorePoolSize(corePoolSize);
        executor.setMaxPoolSize(corePoolSize * 2);
        
        // 较大的队列容量，适合批量处理
        executor.setQueueCapacity(200);
        
        executor.setThreadNamePrefix("Import-");
        executor.setKeepAliveSeconds(300);
        
        // 拒绝策略：抛出异常
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.AbortPolicy());
        
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setAwaitTerminationSeconds(120);
        
        executor.initialize();
        return executor;
    }
}
