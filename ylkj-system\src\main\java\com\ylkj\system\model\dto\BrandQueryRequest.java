package com.ylkj.system.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 品牌查询请求DTO
 * 用于批量查询品牌时的请求参数
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BrandQueryRequest {
    
    /** 品牌名称 */
    private String brandName;
    
    /** 分类ID */
    private Long categoryId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        BrandQueryRequest that = (BrandQueryRequest) o;
        
        if (brandName != null ? !brandName.equals(that.brandName) : that.brandName != null) return false;
        return categoryId != null ? categoryId.equals(that.categoryId) : that.categoryId == null;
    }
    
    @Override
    public int hashCode() {
        int result = brandName != null ? brandName.hashCode() : 0;
        result = 31 * result + (categoryId != null ? categoryId.hashCode() : 0);
        return result;
    }
}
