package com.ylkj.system.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 分类名称到ID的映射DTO
 * 用于批量查询分类ID时的结果映射
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CategoryNameIdMapping {
    
    /** 分类名称 */
    private String categoryName;
    
    /** 分类ID */
    private Long categoryId;
    
    @Override
    public String toString() {
        return "CategoryNameIdMapping{" +
                "categoryName='" + categoryName + '\'' +
                ", categoryId=" + categoryId +
                '}';
    }
    
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        
        CategoryNameIdMapping that = (CategoryNameIdMapping) o;
        
        if (categoryName != null ? !categoryName.equals(that.categoryName) : that.categoryName != null) return false;
        return categoryId != null ? categoryId.equals(that.categoryId) : that.categoryId == null;
    }
    
    @Override
    public int hashCode() {
        int result = categoryName != null ? categoryName.hashCode() : 0;
        result = 31 * result + (categoryId != null ? categoryId.hashCode() : 0);
        return result;
    }
}
