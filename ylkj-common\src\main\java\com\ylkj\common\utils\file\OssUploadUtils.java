package com.ylkj.common.utils.file;

import com.aliyun.oss.OSS;
import com.aliyun.oss.model.ObjectMetadata;
import com.ylkj.common.config.OssConfig;
import com.ylkj.common.exception.file.FileNameLengthLimitExceededException;
import com.ylkj.common.exception.file.FileSizeLimitExceededException;
import com.ylkj.common.exception.file.InvalidExtensionException;
import com.ylkj.common.utils.DateUtils;
import com.ylkj.common.utils.StringUtils;
import com.ylkj.common.utils.spring.SpringUtils;
import com.ylkj.common.utils.uuid.Seq;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.io.FilenameUtils;
import org.bytedeco.ffmpeg.global.avcodec;
import org.bytedeco.ffmpeg.global.avutil;
import org.bytedeco.javacv.FFmpegFrameGrabber;
import org.bytedeco.javacv.FFmpegFrameRecorder;
import org.bytedeco.javacv.Frame;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Objects;

/**
 * 阿里云OSS文件上传工具类
 *
 * <AUTHOR>
 */
public class OssUploadUtils {
    private static final Logger log = LoggerFactory.getLogger(OssUploadUtils.class);

    /**
     * 默认的文件名最大长度 100
     */
    public static final int DEFAULT_FILE_NAME_LENGTH = 100;

    /**
     * OSS配置
     */
    private static final OssConfig ossConfig = SpringUtils.getBean(OssConfig.class);

    /**
     * OSS客户端
     */
    private static final OSS ossClient = SpringUtils.getBean(OSS.class);

    /**
     * 以默认配置上传文件到OSS
     *
     * @param file 上传的文件
     * @return 访问URL
     * @throws IOException
     */
    public static String upload(MultipartFile file) throws IOException {
        try {
            return upload(file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION, false);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 以默认配置上传文件到OSS(无损压缩)
     *
     * @param file 上传的文件
     * @return 访问URL
     * @throws IOException
     */
    public static String uploadWithCompress(MultipartFile file) throws IOException {
        try {
            return upload(file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION, true);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 文件上传
     *
     * @param file             上传的文件
     * @param allowedExtension 允许的文件类型
     * @param isCompress       是否进行压缩(图片、视频)
     * @return 访问URL
     * @throws FileSizeLimitExceededException       如果超出最大大小
     * @throws FileNameLengthLimitExceededException 文件名太长
     * @throws IOException                          比如读写文件出错时
     * @throws InvalidExtensionException            文件校验异常
     */
    public static String upload(MultipartFile file, String[] allowedExtension, boolean isCompress) throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException, InvalidExtensionException {
        int fileNameLength = Objects.requireNonNull(file.getOriginalFilename()).length();
        if (fileNameLength > DEFAULT_FILE_NAME_LENGTH) {
            throw new FileNameLengthLimitExceededException(DEFAULT_FILE_NAME_LENGTH);
        }

        // 校验文件大小
        assertAllowed(file, allowedExtension);

        // 构建文件名
        String fileName = extractFilename(file);

        try {
            // 获取文件输入流
            InputStream inputStream = file.getInputStream();

            // 压缩
            if (isCompress) {
                String mimeType = StringUtils.isNotBlank(file.getContentType()) ? file.getContentType() : "";
                if (mimeType.startsWith("image/")) {
                    inputStream = compressImage(file);
                } else if (mimeType.startsWith("video/")) {
                    inputStream = compressVideo(file);
                }
            }

            // 设置文件元数据
            ObjectMetadata metadata = new ObjectMetadata();
            metadata.setContentLength(inputStream.available());
            metadata.setCacheControl("no-cache");
            metadata.setHeader("Pragma", "no-cache");
            metadata.setContentEncoding("utf-8");
            metadata.setContentType(file.getContentType());

            // 设置文件名
            String ossFileName = ossConfig.getDir() + fileName;

            // 上传文件
            ossClient.putObject(ossConfig.getBucketName(), ossFileName, inputStream, metadata);

            // 关闭输入流
            inputStream.close();

            // 返回文件访问路径
            return ossConfig.getUrlPrefix() + ossFileName;
        } catch (Exception e) {
            log.error("上传文件失败", e);
            throw new IOException(e);
        }
    }

    /**
     * 构建文件名
     */
    public static String extractFilename(MultipartFile file) {
        return String.format("%s/%s_%s.%s", DateUtils.datePath(), FilenameUtils.getBaseName(file.getOriginalFilename()), Seq.getId(Seq.uploadSeqType), getExtension(file));
    }

    /**
     * 文件大小校验
     *
     * @param file             上传的文件
     * @param allowedExtension 允许的文件类型
     * @throws FileSizeLimitExceededException 如果超出最大大小
     * @throws InvalidExtensionException      文件校验异常
     */
    public static void assertAllowed(MultipartFile file, String[] allowedExtension) throws FileSizeLimitExceededException, InvalidExtensionException {
        long size = file.getSize();
        if (size > ossConfig.getMaxSize()) {
            throw new FileSizeLimitExceededException(ossConfig.getMaxSize() / 1024 / 1024);
        }

        String fileName = file.getOriginalFilename();
        String extension = getExtension(file);
        if (allowedExtension != null && !isAllowedExtension(extension, allowedExtension)) {
            if (allowedExtension == MimeTypeUtils.IMAGE_EXTENSION) {
                throw new InvalidExtensionException.InvalidImageExtensionException(allowedExtension, extension, fileName);
            } else if (allowedExtension == MimeTypeUtils.FLASH_EXTENSION) {
                throw new InvalidExtensionException.InvalidFlashExtensionException(allowedExtension, extension, fileName);
            } else if (allowedExtension == MimeTypeUtils.MEDIA_EXTENSION) {
                throw new InvalidExtensionException.InvalidMediaExtensionException(allowedExtension, extension, fileName);
            } else if (allowedExtension == MimeTypeUtils.VIDEO_EXTENSION) {
                throw new InvalidExtensionException.InvalidVideoExtensionException(allowedExtension, extension, fileName);
            } else {
                throw new InvalidExtensionException(allowedExtension, extension, fileName);
            }
        }
    }

    /**
     * 判断MIME类型是否是允许的类型
     *
     * @param extension        上传文件类型
     * @param allowedExtension 允许上传文件类型
     * @return true/false
     */
    public static boolean isAllowedExtension(String extension, String[] allowedExtension) {
        for (String str : allowedExtension) {
            if (str.equalsIgnoreCase(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取文件名的后缀
     *
     * @param file 表单文件
     * @return 后缀名
     */
    public static final String getExtension(MultipartFile file) {
        String extension = FilenameUtils.getExtension(file.getOriginalFilename());
        if (StringUtils.isEmpty(extension)) {
            extension = MimeTypeUtils.getExtension(Objects.requireNonNull(file.getContentType()));
        }
        return extension;
    }

    /**
     * 压缩图片
     *
     * @param file 上传文件
     * @return 压缩后的图片
     * @throws IOException 异常捕捉
     */
    private static InputStream compressImage(MultipartFile file) throws IOException {
        // 记录压缩前大小
        long originalSize = file.getSize();
        log.info("图片压缩前大小: {} bytes ({} MB)", originalSize, String.format("%.2f", originalSize / 1024.0 / 1024.0));

        byte[] compressedBytes;
        try (
                InputStream inputStream = file.getInputStream();
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream()
        ) {
            Thumbnails.of(inputStream).scale(1.0) // 保持原尺寸
                    .outputFormat(getFormat(StringUtils.isNotBlank(file.getOriginalFilename()) ? file.getOriginalFilename() : "")).outputQuality(0.8f) // 无损压缩
                    .toOutputStream(outputStream);

            compressedBytes = outputStream.toByteArray();
        } catch (IOException e) {
            log.error("压缩图片异常", e);
            throw new IOException(e);
        } catch (Exception e) {
            log.warn("图片压缩失败，使用原始文件: {}", e.getMessage());
            // 如果压缩失败，直接使用原始文件
            compressedBytes = file.getBytes();
        }

        // 记录压缩后大小和压缩率
        long compressedSize = compressedBytes.length;
        double compressionRatio = (1.0 - (double) compressedSize / originalSize) * 100;

        log.info("图片压缩后大小: {} bytes ({} MB)", compressedSize, String.format("%.2f", compressedSize / 1024.0 / 1024.0));
        log.info("图片压缩率: {}%", compressionRatio);
        log.info("图片压缩效果: {}", compressionRatio > 0 ? "压缩成功" : "压缩失败");

        return new ByteArrayInputStream(compressedBytes);
    }

    /**
     * 获取图片格式
     *
     * @param filename 文件名称
     * @return 图片格式
     */
    private static String getFormat(String filename) {
        String extension = filename.substring(filename.lastIndexOf(".") + 1).toLowerCase();
        switch (extension) {
            case "jpg":
            case "jpeg":
                return "jpg";
            case "png":
                return "png";
            case "gif":
                return "gif";
            default:
                return "jpg"; // 默认格式
        }
    }

    /**
     * 压缩视频
     *
     * @param multipartFile 文件
     * @return 压缩后文件字节流
     * @throws IOException 异常捕捉
     */
    public static InputStream compressVideo(MultipartFile multipartFile) throws IOException {
        // 记录压缩前大小
        long originalSize = multipartFile.getSize();
        log.info("视频压缩前大小: {} bytes ({} MB)", originalSize, String.format("%.2f", originalSize / 1024.0 / 1024.0));

        // 预检查：如果文件很小，直接返回原文件
        if (originalSize < 5 * 1024 * 1024) { // 小于5MB直接跳过
            log.info("视频文件较小({} MB)，跳过压缩直接使用原文件", String.format("%.2f", originalSize / 1024.0 / 1024.0));
            return new ByteArrayInputStream(multipartFile.getBytes());
        }

        // 创建临时文件
        Path tempInputFile = Files.createTempFile("input-", ".mp4");
        Path tempOutputFile = Files.createTempFile("output-", ".mp4");

        byte[] compressedBytes;
        // 保存上传文件到临时位置
        multipartFile.transferTo(tempInputFile.toFile());

        // 尝试压缩视频
        try {
            compressVideo(tempInputFile.toString(), tempOutputFile.toString(), originalSize);
            compressedBytes = Files.readAllBytes(tempOutputFile);
        } catch (Exception e) {
            log.warn("FFmpeg压缩失败，使用原始文件: {}", e.getMessage());
            // 如果压缩失败，直接使用原始文件
            compressedBytes = Files.readAllBytes(tempInputFile);
        } finally {
            // 删除临时文件
            Files.deleteIfExists(tempInputFile);
            Files.deleteIfExists(tempOutputFile);
        }

        // 记录压缩后大小和压缩率
        long compressedSize = compressedBytes.length;
        double compressionRatio = (1.0 - (double) compressedSize / originalSize) * 100;

        log.info("视频压缩后大小: {} bytes ({} MB)", compressedSize, String.format("%.2f", compressedSize / 1024.0 / 1024.0));
        log.info("视频压缩率: {}%", compressionRatio);
        log.info("视频压缩效果: {}", compressionRatio > 0 ? "压缩成功" : "压缩失败");

        return new ByteArrayInputStream(compressedBytes);
    }

    /**
     * 压缩视频
     *
     * @param inputFile  源文件
     * @param outputFile 压缩后文件
     * @param originalFileSize 原始文件大小（字节）
     * @throws IOException 异常捕捉
     */
    private static void compressVideo(String inputFile, String outputFile, long originalFileSize) throws IOException {
        FFmpegFrameGrabber grabber = new FFmpegFrameGrabber(inputFile);
        FFmpegFrameRecorder recorder = null;

        try {
            grabber.start();

            // 1.获取原始视频信息
            int originalWidth = grabber.getImageWidth();
            int originalHeight = grabber.getImageHeight();
            int audioChannels = grabber.getAudioChannels();
            int sampleRate = grabber.getSampleRate();
            double frameRate = grabber.getFrameRate();
            double duration = grabber.getLengthInTime() / 1000000.0; // 转换为秒

            log.info("原始视频信息 - 分辨率: {}x{}, 帧率: {}, 时长: {}秒, 音频: {}通道/{}Hz", 
                    originalWidth, originalHeight, frameRate, duration, audioChannels, sampleRate);

            // 2.判断是否需要压缩
            boolean needsCompression = shouldCompressVideo(originalFileSize, originalWidth, originalHeight);
            
            if (!needsCompression) {
                log.info("视频无需压缩 - 文件大小: {} MB, 分辨率: {}x{} (低于压缩阈值)", 
                        String.format("%.2f", originalFileSize / 1024.0 / 1024.0), originalWidth, originalHeight);
                // 直接复制文件，不进行压缩
                Files.copy(java.nio.file.Paths.get(inputFile), java.nio.file.Paths.get(outputFile));
                return;
            }

            log.info("视频需要压缩 - 文件大小: {} MB, 分辨率: {}x{} (超过压缩阈值)", 
                    String.format("%.2f", originalFileSize / 1024.0 / 1024.0), originalWidth, originalHeight);

            // 3.动态计算目标比特率
            long targetVideoBitrate = calculateVideoBitrate(originalFileSize, duration, originalWidth, originalHeight);

            // 4.视频编码设置 - 高质量压缩
            recorder = new FFmpegFrameRecorder(outputFile, originalWidth, originalHeight, audioChannels);
            recorder.setVideoCodec(avcodec.AV_CODEC_ID_H264);
            recorder.setFormat("mp4");
            recorder.setFrameRate(frameRate > 0 ? frameRate : 25.0); // 保持原始帧率
            recorder.setPixelFormat(avutil.AV_PIX_FMT_YUV420P);
            
            // 使用动态比特率而不是CRF
            recorder.setVideoBitrate((int) targetVideoBitrate);
            recorder.setVideoOption("preset", "medium"); // 使用medium平衡速度和质量
            recorder.setVideoOption("profile", "high");   // 高级配置文件

            log.info("压缩设置 - 视频比特率: {} kbps, 预设: medium, 保持原始分辨率和帧率", targetVideoBitrate / 1000);

            // 5.音频编码设置（如果有音频）
            if (audioChannels > 0 && sampleRate > 0) {
                recorder.setAudioCodec(avcodec.AV_CODEC_ID_AAC);
                recorder.setAudioBitrate(192000); // 提高到192kbps，保持更好的音频质量
                recorder.setAudioChannels(audioChannels);
                recorder.setSampleRate(sampleRate);
            }

            recorder.start();

            Frame frame;
            while ((frame = grabber.grabFrame()) != null) {
                recorder.record(frame);
            }
        } catch (Exception e) {
            log.error("视频压缩过程中发生错误: {}", e.getMessage(), e);
            throw new IOException("视频压缩失败: " + e.getMessage(), e);
        } finally {
            try {
                if (recorder != null) {
                    recorder.stop();
                    recorder.release();
                }
            } catch (Exception e) {
                log.warn("关闭视频录制器时发生错误: {}", e.getMessage());
            }
            try {
                grabber.stop();
                grabber.release();
            } catch (Exception e) {
                log.warn("关闭视频抓取器时发生错误: {}", e.getMessage());
            }
        }
    }


    /**
     * 判断是否需要压缩视频
     *
     * @param fileSize 文件大小（字节）
     * @param width 视频宽度
     * @param height 视频高度
     * @return 是否需要压缩
     */
    private static boolean shouldCompressVideo(long fileSize, int width, int height) {
        // 文件大小阈值：10MB
        long fileSizeThreshold = 10 * 1024 * 1024; // 10MB

        // 分辨率阈值：1280x720 (720p)
        int widthThreshold = 1280;
        int heightThreshold = 720;

        // 像素总数阈值
        long pixelThreshold = (long) widthThreshold * heightThreshold;
        long currentPixels = (long) width * height;

        // 满足以下任一条件就需要压缩：
        // 1. 文件大小超过阈值
        // 2. 分辨率超过阈值
        boolean fileSizeExceeds = fileSize > fileSizeThreshold;
        boolean resolutionExceeds = currentPixels > pixelThreshold;

        log.info("压缩判断 - 文件大小: {} MB (阈值: {} MB), 分辨率: {}x{} (阈值: {}x{}), 需要压缩: {}",
                String.format("%.2f", fileSize / 1024.0 / 1024.0),
                fileSizeThreshold / 1024 / 1024,
                width, height,
                widthThreshold, heightThreshold,
                fileSizeExceeds || resolutionExceeds);

        return fileSizeExceeds || resolutionExceeds;
    }

    /**
     * 动态计算视频比特率
     *
     * @param originalFileSize 原始文件大小（字节）
     * @param duration 视频时长（秒）
     * @param width 视频宽度
     * @param height 视频高度
     * @return 计算得出的视频比特率
     */
    private static long calculateVideoBitrate(long originalFileSize, double duration, int width, int height) {
        // 动态计算目标比特率
        double targetCompressionRatio = 0.25; // 目标压缩率25%（保留75%大小）
        // 压缩率范围：0.2=20%, 0.25=25%, 0.3=30%
        long targetSizeBytes = (long) (originalFileSize * (1.0 - targetCompressionRatio));

        // 计算目标视频比特率（考虑音频占用）
        int audioBitrate = 192000; // 192kbps音频
        long targetVideoBitrate = 0;

        if (duration > 0) {
            // 总目标比特率 = 目标文件大小 * 8 / 时长
            long totalTargetBitrate = (long) ((targetSizeBytes * 8.0) / duration);
            // 视频比特率 = 总比特率 - 音频比特率
            targetVideoBitrate = Math.max(totalTargetBitrate - audioBitrate, 500000); // 最小500kbps

            // 设置合理的比特率上限，避免过高
            long maxBitrate = Math.min(originalFileSize * 8 / (long)duration, 10000000); // 最大10Mbps
            targetVideoBitrate = Math.min(targetVideoBitrate, maxBitrate);
        } else {
            // 如果无法获取时长，使用基于分辨率的估算
            long pixels = (long) width * height;
            targetVideoBitrate = Math.max(pixels / 100, 1000000); // 基于像素数估算，最小1Mbps
            targetVideoBitrate = Math.min(targetVideoBitrate, 5000000); // 最大5Mbps（无时长时保守估计）
        }

        log.info("动态比特率计算 - 原始大小: {} MB, 目标压缩率: {}% (保留{}%), 目标大小: {} MB, 计算得出视频比特率: {} kbps",
                String.format("%.2f", originalFileSize / 1024.0 / 1024.0),
                (int)(targetCompressionRatio * 100),
                (int)((1.0 - targetCompressionRatio) * 100),
                String.format("%.2f", targetSizeBytes / 1024.0 / 1024.0),
                targetVideoBitrate / 1000);

        return targetVideoBitrate;
    }
} 