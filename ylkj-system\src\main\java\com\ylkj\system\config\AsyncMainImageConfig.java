package com.ylkj.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 异步主图更新配置
 * 
 * <AUTHOR>
 * @date 2025-08-04
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "async.main-image")
public class AsyncMainImageConfig {
    
    /**
     * 批处理大小，默认10个商品一批
     */
    private int batchSize = 10;
    
    /**
     * 线程池大小，默认3个线程
     */
    private int threadPoolSize = 3;
    
    /**
     * 请求延迟时间（毫秒），默认1500ms
     */
    private long requestDelay = 1500;
    
    /**
     * 是否启用并行处理，默认true
     */
    private boolean enableParallel = true;
    
    /**
     * 是否启用预过滤，默认true
     */
    private boolean enablePreFilter = true;
    
    /**
     * 是否启用批量数据库更新，默认false
     */
    private boolean enableBatchUpdate = true;
    
    /**
     * 超时时间（秒），默认300秒
     */
    private int timeoutSeconds = 300;
    
    /**
     * 重试次数，默认3次
     */
    private int retryCount = 3;
    
    /**
     * 重试延迟时间（毫秒），默认5000ms
     */
    private long retryDelay = 5000;
}
