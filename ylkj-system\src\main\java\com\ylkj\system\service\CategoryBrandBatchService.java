package com.ylkj.system.service;

import com.ylkj.system.model.domain.OmgProducts;
import com.ylkj.system.model.dto.BrandQueryRequest;

import java.util.List;
import java.util.Map;

/**
 * 分类和品牌批量处理服务接口
 * 用于优化商品导入时的分类和品牌处理性能
 * 
 * <AUTHOR>
 * @date 2025-08-05
 */
public interface CategoryBrandBatchService {
    
    /**
     * 批量处理商品的分类和品牌信息
     * 
     * @param products 商品列表
     */
    void processCategoriesAndBrandsBatch(List<OmgProducts> products);
    
    /**
     * 批量获取或创建分类ID映射
     * 分类不存在会自动创建
     * 
     * @param categoryNames 分类名称列表
     * @return 分类名称到ID的映射 Map<分类名称, 分类ID>
     */
    Map<String, Long> batchGetOrCreateCategoryIds(List<String> categoryNames);
    
    /**
     * 批量获取或创建品牌ID映射
     * 品牌不存在会自动创建
     * 
     * @param brandRequests 品牌查询请求列表
     * @return 品牌键到品牌ID的映射 Map<品牌名称_分类ID, 品牌ID>
     */
    Map<String, Long> batchGetOrCreateBrandIds(List<BrandQueryRequest> brandRequests);
    
    /**
     * 批量处理分类
     * 
     * @param categoryNames 分类名称列表
     * @return 分类名称到ID的映射
     */
    Map<String, Long> batchProcessCategoriesWithInsertIgnore(List<String> categoryNames);
    
    /**
     * 批量处理品牌
     * 
     * @param brandRequests 品牌查询请求列表
     * @return 品牌键到品牌ID的映射
     */
    Map<String, Long> batchProcessBrandsWithInsertIgnore(List<BrandQueryRequest> brandRequests);
    
    /**
     * 获取批量处理的性能统计信息
     * 
     * @return 性能统计信息
     */
    String getPerformanceStats();
    
    /**
     * 清理缓存和重置统计信息
     * 在大批量处理完成后调用，释放内存
     */
    void clearCacheAndStats();
}
