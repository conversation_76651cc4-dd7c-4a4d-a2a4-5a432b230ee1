package com.ylkj.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 统一异步配置
 * 针对4核8G+5M带宽海外服务器优化
 * 
 * <AUTHOR>
 * @date 2025-08-06
 */
@Data
@Component
@ConfigurationProperties(prefix = "async")
public class UnifiedAsyncConfig {
    
    /**
     * QC更新配置
     */
    private QcUpdateConfig qcUpdate = new QcUpdateConfig();
    
    /**
     * 主图更新配置
     */
    private MainImageConfig mainImage = new MainImageConfig();
    
    /**
     * 商品导入配置
     */
    private ProductImportConfig productImport = new ProductImportConfig();
    
    @Data
    public static class QcUpdateConfig {
        /**
         * 批处理大小
         */
        private int batchSize = 20;

        /**
         * API请求延迟时间 (毫秒)
         */
        private long requestDelay = 800;

        /**
         * 任务状态缓存过期时间 (小时)
         */
        private int taskStatusExpireHours = 24;

        /**
         * 是否启用批量数据库更新
         */
        private boolean enableBatchUpdate = true;

        /**
         * 超时时间 (秒)
         */
        private int timeoutSeconds = 7200;

        /**
         * 重试次数
         */
        private int retryCount = 5;

        /**
         * 重试延迟时间 (毫秒)
         */
        private long retryDelay = 8000;

        /**
         * 最大并发API调用数
         */
        private int maxConcurrentApiCalls = 2;
    }

    @Data
    public static class MainImageConfig {
        /**
         * 是否启用增强版异步主图更新服务
         */
        private boolean enhanced = true;
        
        /**
         * 批处理大小
         */
        private int batchSize = 15;
        
        /**
         * 线程池大小
         */
        private int threadPoolSize = 2;
        
        /**
         * 请求延迟时间 (毫秒)
         */
        private long requestDelay = 1200;
        
        /**
         * 是否启用并行处理
         */
        private boolean enableParallel = true;
        
        /**
         * 是否启用预过滤
         */
        private boolean enablePreFilter = true;
        
        /**
         * 是否启用批量数据库更新
         */
        private boolean enableBatchUpdate = true;
        
        /**
         * 超时时间 (秒)
         */
        private int timeoutSeconds = 1800;
        
        /**
         * 重试次数
         */
        private int retryCount = 4;
        
        /**
         * 重试延迟时间 (毫秒)
         */
        private long retryDelay = 6000;
    }
    
    @Data
    public static class ProductImportConfig {
        /**
         * 批处理大小
         */
        private int batchSize = 100;
        
        /**
         * 线程池大小
         */
        private int threadPoolSize = 2;
        
        /**
         * 数据库批量写入大小
         */
        private int dbBatchSize = 200;
        
        /**
         * 超时时间 (秒)
         */
        private int timeoutSeconds = 3600;
    }
    
    /**
     * 获取当前环境下推荐的线程池配置
     * 基于服务器规格自动调整
     */
    public ThreadPoolConfig getOptimalThreadPoolConfig() {
        return new ThreadPoolConfig();
    }
    
    @Data
    public static class ThreadPoolConfig {
        /**
         * 核心线程数 - 基于CPU核心数
         */
        private int corePoolSize = 3;
        
        /**
         * 最大线程数 - 避免过度上下文切换
         */
        private int maxPoolSize = 6;
        
        /**
         * 队列容量 - 平衡内存使用和响应性
         */
        private int queueCapacity = 150;
        
        /**
         * 线程名前缀
         */
        private String threadNamePrefix = "async-unified-";
        
        /**
         * 线程空闲时间 (秒)
         */
        private int keepAliveSeconds = 300;
    }
} 