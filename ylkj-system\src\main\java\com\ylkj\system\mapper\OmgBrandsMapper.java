package com.ylkj.system.mapper;

import java.util.List;
import com.ylkj.system.model.domain.OmgBrands;
import com.ylkj.system.model.dto.BrandNameIdMapping;
import com.ylkj.system.model.dto.BrandQueryRequest;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

/**
 * omg_品牌Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-05-13
 */
public interface OmgBrandsMapper extends BaseMapper<OmgBrands>
{
    /**
     * 查询omg_品牌
     * 
     * @param brandId omg_品牌主键
     * @return omg_品牌
     */
    public OmgBrands selectOmgBrandsByBrandId(Long brandId);

    /**
     * 查询omg_品牌列表
     * 
     * @param omgBrands omg_品牌
     * @return omg_品牌集合
     */
    public List<OmgBrands> selectOmgBrandsList(OmgBrands omgBrands);

    /**
     * 新增omg_品牌
     * 
     * @param omgBrands omg_品牌
     * @return 结果
     */
    public int insertOmgBrands(OmgBrands omgBrands);

    /**
     * 修改omg_品牌
     * 
     * @param omgBrands omg_品牌
     * @return 结果
     */
    public int updateOmgBrands(OmgBrands omgBrands);

    /**
     * 删除omg_品牌
     * 
     * @param brandId omg_品牌主键
     * @return 结果
     */
    public int deleteOmgBrandsByBrandId(Long brandId);

    /**
     * 批量删除omg_品牌
     * 
     * @param brandIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteOmgBrandsByBrandIds(Long[] brandIds);

    /**
     * 根据分类id查询品牌列表
     * @param categoryId
     * @return
     */
    List<OmgBrands> getBrandListByCategoryId(Long categoryId);

    /**
     * 根据品牌名称查询品牌id
     * @param secondCategoryName
     * @return
     */
    Long selectBrandIdByName(@Param("secondCategoryName") String secondCategoryName,
                             @Param("categoryId") Long categoryId);

    /**
     * 批量查询品牌ID映射
     * @param brandRequests 品牌查询请求列表（包含品牌名称和分类ID）
     * @return 品牌名称+分类ID到品牌ID的映射列表
     */
    List<BrandNameIdMapping> batchSelectBrandIdsByNames(@Param("brandRequests") List<BrandQueryRequest> brandRequests);

    /**
     * 批量插入品牌
     * @param brands 品牌列表
     * @return 插入成功的记录数
     */
    int batchInsertBrandsIgnore(@Param("brands") List<OmgBrands> brands);


}
