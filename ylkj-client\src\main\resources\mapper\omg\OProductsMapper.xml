<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ylkj.mapper.omg.OProductsMapper">
    <!-- 可复用的商品字段列表 -->
    <sql id="Product_Column_List">
        p.product_id
        , p.seller_id, p.name, p.slug, p.description, p.price, p.original_price,
        p.main_image, p.sku, p.stock, p.likes, p.views, p.rating, p.total_ratings, p.status,
        p.created_at, p.updated_at, p.category_id,p.tag,p.from_url,p.product_status,p.platform,p.merchant,p.qc,p.average_arrival,p.weight,p.virtual_views,p.product_attributes,p.video
    </sql>
    <update id="updateViews" parameterType="java.lang.Long">
        update  omg_products
        SET views = views + 1
        WHERE product_id = #{productId}
    </update>
    <update id="likeProduct">
        update  omg_products
        SET likes = likes + 1
        WHERE product_id = #{productId}
    </update>
    <update id="unLikeProduct">
        update  omg_products
        SET likes = likes - 1
        WHERE product_id = #{productId}
    </update>

    <!--关键词搜索商品-->
    <select id="searchProducts" resultType="com.ylkj.model.domain.omg.OProducts">
        SELECT
        <include refid="Product_Column_List"/>
        FROM
        omg_products p
        LEFT JOIN
        omg_categories c ON p.category_id = c.category_id
        <where>
            p.status = 'active'
            <if test="keyword != null and keyword != ''">
                AND (
                p.name LIKE CONCAT('%', #{keyword}, '%')
                OR p.description LIKE CONCAT('%', #{keyword}, '%')
                OR p.sku LIKE CONCAT('%', #{keyword}, '%')
                OR c.name LIKE CONCAT('%', #{keyword}, '%')
                )
            </if>
            <if test="category != null and category != ''">
                AND (
                c.category_id = #{category}
                OR c.slug = #{category}
                OR c.name = #{category}
                OR EXISTS (
                WITH RECURSIVE category_tree AS (
                SELECT
                category_id
                FROM
                omg_categories
                WHERE
                category_id = #{category}
                OR slug = #{category}
                OR name = #{category}

                UNION ALL

                SELECT
                c2.category_id
                FROM
                omg_categories c2
                JOIN
                category_tree ct ON c2.parent_category_id = ct.category_id
                )
                SELECT 1 FROM category_tree WHERE category_id = p.category_id
                )
                )
            </if>
            <if test="minPrice != null">
                AND p.price >= #{minPrice}
            </if>
            <if test="maxPrice != null">
                AND p.price &lt;= #{maxPrice}
            </if>
        </where>
        ORDER BY
        <choose>
            <when test="keyword != null and keyword != ''">
                CASE
                WHEN p.name LIKE CONCAT('%', #{keyword}, '%') THEN 1
                WHEN c.name LIKE CONCAT('%', #{keyword}, '%') THEN 2
                WHEN p.sku LIKE CONCAT('%', #{keyword}, '%') THEN 3
                ELSE 4
                END,
            </when>
        </choose>
        p.rating DESC, p.created_at DESC
    </select>

    <!--根据分类id和品牌id获取商品列表-->
    <select id="getProductsByCategory" resultType="com.ylkj.model.domain.omg.OProducts">
        SELECT
        <include refid="Product_Column_List" />,
        b.brand_id
        FROM
        omg_products p LEFT JOIN omg_product_brands pb ON p.product_id = pb.product_id
                       LEFT JOIN omg_brands b ON pb.brand_id = b.brand_id
        <where>
         p.status = 'active'
        <if test='categoryId != null'> AND p.category_id = #{categoryId} </if>
        <if test='brandId != null'> AND b.brand_id = #{brandId} </if>
        <if test="merchant != null">AND p.merchant LIKE CONCAT('%', #{merchant}, '%')</if>
        </where>

    </select>

    <select id="getProductById" resultType="com.ylkj.model.vo.OProductsVO" parameterType="long">
        select * from omg_products where product_id = #{productId}
    </select>

    <!--<select id="getRecommendProducts" resultType="com.ylkj.model.domain.omg.OProducts">
        SELECT
            p.product_id,
            p.name,
            p.description,
            p.price,
            p.original_price,
            p.main_image,
            p.sku,
            p.stock,
            p.likes,
            p.views,
            p.rating,
            p.total_ratings,
            p.status,
            p.category_id,
            b.brand_id, 
            b.name AS brand_name
        FROM
            omg_products p
                LEFT JOIN omg_product_brands pb ON p.product_id = pb.product_id
                LEFT JOIN omg_brands b ON pb.brand_id = b.brand_id
                LEFT JOIN omg_categories c ON p.category_id = c.category_id
        WHERE
        p.status = 'active'
        <if test="categoryId != null">
            AND p.category_id = #{categoryId}
        </if>
        <if test="categoryId == null">
            &lt;!&ndash; 无分类时，返回热门商品 &ndash;&gt;
            AND p.likes > (SELECT AVG(likes) * 0.5 FROM omg_products WHERE likes > 0)
        </if>
        ORDER BY
        <choose>
            <when test="categoryId != null">
                &lt;!&ndash; 当有分类ID时，优先显示该分类的商品 &ndash;&gt;
                CASE WHEN p.category_id = #{categoryId} THEN 1 ELSE 2 END,
            </when>
            <otherwise>
                &lt;!&ndash; 无分类时，按热度排序 &ndash;&gt;
            </otherwise>
        </choose>
        p.rating DESC, p.likes DESC, p.views DESC
        LIMIT #{limit}
    </select>-->
    <!--推荐商品-->
    <select id="getRecommendProducts" resultType="com.ylkj.model.domain.omg.OProducts">
        SELECT
        <include refid="Product_Column_List" />
        FROM
            omg_products p
        WHERE
            p.status = 'active'
          AND (
            (
                p.category_id = #{categoryId}
                    AND NOT EXISTS (
                    SELECT 1
                    FROM omg_product_brands pb
                    WHERE pb.product_id = p.product_id
                      AND pb.brand_id = #{brandId}
                )
                )
                OR
            (
                EXISTS (
                    SELECT 1
                    FROM omg_product_brands pb
                    WHERE pb.product_id = p.product_id
                      AND pb.brand_id = #{brandId}
                )
                    AND p.category_id != #{categoryId}
                )
            )
        ORDER BY RAND()
        LIMIT 20
    </select>
    <select id="getSkuProducts" resultType="com.ylkj.model.vo.OSkuProductsVO">
        select product_id, sku, main_image from omg_products where sku  = #{id}
    </select>
    <select id="getAllMerchant" resultType="java.lang.String">
        select DISTINCT merchant from omg_products
    </select>
    
    <!-- 获取指定product_status的产品列表 -->
    <select id="getProductsByStatus" resultType="com.ylkj.model.domain.omg.OProducts">
        SELECT 
        <include refid="Product_Column_List" />
        FROM omg_products p
        <where>
            p.status = 'active'
            <if test="statusList != null and statusList.size() > 0">
                AND p.product_status IN
                <foreach collection="statusList" item="status" open="(" separator="," close=")">
                    #{status}
                </foreach>
            </if>
        </where>
        ORDER BY p.updated_at DESC
    </select>
    <select id="getAllRecommendProducts" resultType="com.ylkj.model.domain.omg.OProducts">
        SELECT
        <include refid="Product_Column_List" />,
        b.brand_id
        FROM
        omg_products p LEFT JOIN omg_product_brands pb ON p.product_id = pb.product_id
        LEFT JOIN omg_brands b ON pb.brand_id = b.brand_id
        <where>
            p.status = 'active' and p.product_status = 2
        </where>
    </select>

    <!-- 批量更新商品QC数量 -->
    <update id="batchUpdateProductsQc" parameterType="java.util.List">
        <if test="list != null and list.size() > 0">
            UPDATE omg_products
            SET qc = CASE product_id
                <foreach collection="list" item="item">
                    WHEN #{item.productId} THEN #{item.qc}
                </foreach>
                ELSE qc
            END,
            updated_at = NOW()
            WHERE product_id IN
            <foreach collection="list" item="item" open="(" separator="," close=")">
                #{item.productId}
            </foreach>
        </if>
    </update>

    <!-- 根据SKU批量更新商品QC数量 -->
    <update id="batchUpdateProductsQcBySku" parameterType="java.util.Map">
        <if test="skuQcMap != null and skuQcMap.size() > 0">
            UPDATE omg_products
            SET qc = CASE sku
                <foreach collection="skuQcMap" item="value" index="key">
                    WHEN #{key} THEN #{value}
                </foreach>
                ELSE qc
            END,
            updated_at = NOW()
            WHERE sku IN
            <foreach collection="skuQcMap" item="value" index="key" open="(" separator="," close=")">
                #{key}
            </foreach>
        </if>
    </update>

    <!-- 根据SKU更新单个商品的QC数量 -->
    <update id="updateProductQcBySku">
        UPDATE omg_products
        SET qc = #{qc}, updated_at = NOW()
        WHERE sku = #{sku}
    </update>
</mapper>